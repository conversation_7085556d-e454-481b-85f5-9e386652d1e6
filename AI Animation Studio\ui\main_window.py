# AI Animation Studio - 主窗口
# 版权所有 (c) 2024 AI Animation Studio

import os
from PyQt6.QtWidgets import *
from PyQt6.QtCore import *
from PyQt6.QtGui import *

from core.data.config import Config, ProjectConfig
from core.data.project_data import ProjectData
from core.data.enums import LayoutMode

class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self, config: Config):
        super().__init__()
        
        self.config = config
        self.project_data = None
        self.current_layout_mode = LayoutMode.EDIT
        
        # 初始化界面
        self.init_ui()
        self.setup_menu_bar()
        self.setup_tool_bar()
        self.setup_status_bar()
        self.setup_central_widget()
        self.setup_dock_widgets()
        
        # 连接信号
        self.connect_signals()
        
        # 设置窗口属性
        self.setup_window_properties()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("AI Animation Studio - AI动画工作站")
        self.setMinimumSize(1200, 800)
        
        # 设置窗口图标
        icon_path = "assets/icons/app_icon.png"
        if os.path.exists(icon_path):
            self.setWindowIcon(QIcon(icon_path))
    
    def setup_menu_bar(self):
        """设置菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu("文件(&F)")
        
        # 新建项目
        new_action = QAction("新建项目(&N)", self)
        new_action.setShortcut(QKeySequence.StandardKey.New)
        new_action.setStatusTip("创建新的动画项目")
        new_action.triggered.connect(self.new_project)
        file_menu.addAction(new_action)
        
        # 打开项目
        open_action = QAction("打开项目(&O)", self)
        open_action.setShortcut(QKeySequence.StandardKey.Open)
        open_action.setStatusTip("打开现有的动画项目")
        open_action.triggered.connect(self.open_project)
        file_menu.addAction(open_action)
        
        # 最近项目
        recent_menu = file_menu.addMenu("最近项目(&R)")
        self.update_recent_projects_menu(recent_menu)
        
        file_menu.addSeparator()
        
        # 保存项目
        save_action = QAction("保存项目(&S)", self)
        save_action.setShortcut(QKeySequence.StandardKey.Save)
        save_action.setStatusTip("保存当前项目")
        save_action.triggered.connect(self.save_project)
        file_menu.addAction(save_action)
        
        # 另存为
        save_as_action = QAction("另存为(&A)", self)
        save_as_action.setShortcut(QKeySequence.StandardKey.SaveAs)
        save_as_action.setStatusTip("将项目保存到新位置")
        save_as_action.triggered.connect(self.save_project_as)
        file_menu.addAction(save_as_action)
        
        file_menu.addSeparator()
        
        # 导入素材
        import_action = QAction("导入素材(&I)", self)
        import_action.setStatusTip("导入图片、音频等素材")
        import_action.triggered.connect(self.import_assets)
        file_menu.addAction(import_action)
        
        file_menu.addSeparator()
        
        # 导出
        export_menu = file_menu.addMenu("导出(&E)")
        
        export_html_action = QAction("导出HTML", self)
        export_html_action.triggered.connect(self.export_html)
        export_menu.addAction(export_html_action)
        
        export_video_action = QAction("导出视频", self)
        export_video_action.triggered.connect(self.export_video)
        export_menu.addAction(export_video_action)
        
        file_menu.addSeparator()
        
        # 退出
        exit_action = QAction("退出(&X)", self)
        exit_action.setShortcut(QKeySequence.StandardKey.Quit)
        exit_action.setStatusTip("退出应用程序")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 编辑菜单
        edit_menu = menubar.addMenu("编辑(&E)")
        
        undo_action = QAction("撤销(&U)", self)
        undo_action.setShortcut(QKeySequence.StandardKey.Undo)
        undo_action.triggered.connect(self.undo)
        edit_menu.addAction(undo_action)
        
        redo_action = QAction("重做(&R)", self)
        redo_action.setShortcut(QKeySequence.StandardKey.Redo)
        redo_action.triggered.connect(self.redo)
        edit_menu.addAction(redo_action)
        
        edit_menu.addSeparator()
        
        copy_action = QAction("复制(&C)", self)
        copy_action.setShortcut(QKeySequence.StandardKey.Copy)
        copy_action.triggered.connect(self.copy)
        edit_menu.addAction(copy_action)
        
        paste_action = QAction("粘贴(&V)", self)
        paste_action.setShortcut(QKeySequence.StandardKey.Paste)
        paste_action.triggered.connect(self.paste)
        edit_menu.addAction(paste_action)
        
        # 视图菜单
        view_menu = menubar.addMenu("视图(&V)")
        
        # 布局模式
        layout_group = QActionGroup(self)
        
        edit_mode_action = QAction("编辑模式", self)
        edit_mode_action.setCheckable(True)
        edit_mode_action.setChecked(True)
        edit_mode_action.triggered.connect(lambda: self.switch_layout_mode(LayoutMode.EDIT))
        layout_group.addAction(edit_mode_action)
        view_menu.addAction(edit_mode_action)
        
        preview_mode_action = QAction("预览模式", self)
        preview_mode_action.setCheckable(True)
        preview_mode_action.triggered.connect(lambda: self.switch_layout_mode(LayoutMode.PREVIEW))
        layout_group.addAction(preview_mode_action)
        view_menu.addAction(preview_mode_action)
        
        view_menu.addSeparator()
        
        # 显示/隐藏面板
        self.timeline_dock_action = QAction("时间轴面板", self)
        self.timeline_dock_action.setCheckable(True)
        self.timeline_dock_action.setChecked(True)
        view_menu.addAction(self.timeline_dock_action)
        
        self.stage_dock_action = QAction("舞台面板", self)
        self.stage_dock_action.setCheckable(True)
        self.stage_dock_action.setChecked(True)
        view_menu.addAction(self.stage_dock_action)
        
        self.preview_dock_action = QAction("预览面板", self)
        self.preview_dock_action.setCheckable(True)
        self.preview_dock_action.setChecked(True)
        view_menu.addAction(self.preview_dock_action)
        
        # 工具菜单
        tools_menu = menubar.addMenu("工具(&T)")
        
        ai_generate_action = QAction("AI生成动画", self)
        ai_generate_action.setShortcut("Ctrl+G")
        ai_generate_action.triggered.connect(self.show_ai_generator)
        tools_menu.addAction(ai_generate_action)
        
        rules_editor_action = QAction("动画规则编辑器", self)
        rules_editor_action.triggered.connect(self.show_rules_editor)
        tools_menu.addAction(rules_editor_action)
        
        tools_menu.addSeparator()
        
        settings_action = QAction("设置(&S)", self)
        settings_action.triggered.connect(self.show_settings)
        tools_menu.addAction(settings_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu("帮助(&H)")
        
        help_action = QAction("使用帮助(&H)", self)
        help_action.setShortcut(QKeySequence.StandardKey.HelpContents)
        help_action.triggered.connect(self.show_help)
        help_menu.addAction(help_action)
        
        about_action = QAction("关于(&A)", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def setup_tool_bar(self):
        """设置工具栏"""
        toolbar = self.addToolBar("主工具栏")
        toolbar.setToolButtonStyle(Qt.ToolButtonStyle.ToolButtonTextUnderIcon)
        
        # 新建项目
        new_action = QAction("新建", self)
        new_action.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_FileIcon))
        new_action.triggered.connect(self.new_project)
        toolbar.addAction(new_action)
        
        # 打开项目
        open_action = QAction("打开", self)
        open_action.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_DirOpenIcon))
        open_action.triggered.connect(self.open_project)
        toolbar.addAction(open_action)
        
        # 保存项目
        save_action = QAction("保存", self)
        save_action.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_DialogSaveButton))
        save_action.triggered.connect(self.save_project)
        toolbar.addAction(save_action)
        
        toolbar.addSeparator()
        
        # 播放控制
        self.play_action = QAction("播放", self)
        self.play_action.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_MediaPlay))
        self.play_action.triggered.connect(self.toggle_playback)
        toolbar.addAction(self.play_action)
        
        stop_action = QAction("停止", self)
        stop_action.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_MediaStop))
        stop_action.triggered.connect(self.stop_playback)
        toolbar.addAction(stop_action)
        
        toolbar.addSeparator()
        
        # AI生成
        ai_action = QAction("AI生成", self)
        ai_action.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_ComputerIcon))
        ai_action.triggered.connect(self.show_ai_generator)
        toolbar.addAction(ai_action)
    
    def setup_status_bar(self):
        """设置状态栏"""
        self.status_bar = self.statusBar()
        
        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_bar.addWidget(self.status_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.status_bar.addPermanentWidget(self.progress_bar)
        
        # 时间信息
        self.time_label = QLabel("00:00 / 00:00")
        self.status_bar.addPermanentWidget(self.time_label)
        
        # 项目信息
        self.project_label = QLabel("无项目")
        self.status_bar.addPermanentWidget(self.project_label)
    
    def setup_central_widget(self):
        """设置中央部件"""
        # 创建中央分割器
        self.central_splitter = QSplitter(Qt.Orientation.Horizontal)
        self.setCentralWidget(self.central_splitter)
        
        # 左侧面板（舞台区域）
        self.stage_widget = QWidget()
        self.stage_widget.setMinimumWidth(400)
        self.central_splitter.addWidget(self.stage_widget)
        
        # 右侧面板（预览区域）
        self.preview_widget = QWidget()
        self.preview_widget.setMinimumWidth(300)
        self.central_splitter.addWidget(self.preview_widget)
        
        # 设置分割比例
        self.central_splitter.setSizes([800, 400])
    
    def setup_dock_widgets(self):
        """设置停靠部件"""
        # 时间轴停靠部件
        self.timeline_dock = QDockWidget("时间轴", self)
        self.timeline_dock.setAllowedAreas(Qt.DockWidgetArea.BottomDockWidgetArea)
        
        timeline_widget = QWidget()
        timeline_layout = QVBoxLayout(timeline_widget)
        timeline_layout.addWidget(QLabel("时间轴面板 - 待实现"))
        
        self.timeline_dock.setWidget(timeline_widget)
        self.addDockWidget(Qt.DockWidgetArea.BottomDockWidgetArea, self.timeline_dock)
        
        # 属性面板停靠部件
        self.properties_dock = QDockWidget("属性", self)
        self.properties_dock.setAllowedAreas(
            Qt.DockWidgetArea.LeftDockWidgetArea | Qt.DockWidgetArea.RightDockWidgetArea
        )
        
        properties_widget = QWidget()
        properties_layout = QVBoxLayout(properties_widget)
        properties_layout.addWidget(QLabel("属性面板 - 待实现"))
        
        self.properties_dock.setWidget(properties_widget)
        self.addDockWidget(Qt.DockWidgetArea.RightDockWidgetArea, self.properties_dock)
        
        # 素材库停靠部件
        self.assets_dock = QDockWidget("素材库", self)
        self.assets_dock.setAllowedAreas(
            Qt.DockWidgetArea.LeftDockWidgetArea | Qt.DockWidgetArea.RightDockWidgetArea
        )
        
        assets_widget = QWidget()
        assets_layout = QVBoxLayout(assets_widget)
        assets_layout.addWidget(QLabel("素材库面板 - 待实现"))
        
        self.assets_dock.setWidget(assets_widget)
        self.addDockWidget(Qt.DockWidgetArea.LeftDockWidgetArea, self.assets_dock)
    
    def connect_signals(self):
        """连接信号"""
        # 停靠部件可见性
        if hasattr(self, 'timeline_dock_action'):
            self.timeline_dock_action.toggled.connect(self.timeline_dock.setVisible)
            self.timeline_dock.visibilityChanged.connect(self.timeline_dock_action.setChecked)
        
        if hasattr(self, 'stage_dock_action'):
            self.stage_dock_action.toggled.connect(self.stage_widget.setVisible)
        
        if hasattr(self, 'preview_dock_action'):
            self.preview_dock_action.toggled.connect(self.preview_widget.setVisible)
    
    def setup_window_properties(self):
        """设置窗口属性"""
        # 设置窗口状态
        if self.config.ui.window_maximized:
            self.showMaximized()
        else:
            self.resize(self.config.ui.window_width, self.config.ui.window_height)
        
        # 居中显示
        self.center_window()
    
    def center_window(self):
        """窗口居中"""
        screen = QApplication.primaryScreen().geometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)

    # === 项目管理方法 ===

    def new_project(self):
        """新建项目"""
        # 检查是否需要保存当前项目
        if self.project_data and not self.check_save_current_project():
            return

        # 创建新项目对话框
        dialog = QDialog(self)
        dialog.setWindowTitle("新建项目")
        dialog.setModal(True)
        dialog.resize(400, 300)

        layout = QVBoxLayout(dialog)

        # 项目信息
        form_layout = QFormLayout()

        name_edit = QLineEdit("新建项目")
        form_layout.addRow("项目名称:", name_edit)

        author_edit = QLineEdit()
        form_layout.addRow("作者:", author_edit)

        description_edit = QTextEdit()
        description_edit.setMaximumHeight(100)
        form_layout.addRow("描述:", description_edit)

        layout.addLayout(form_layout)

        # 按钮
        button_layout = QHBoxLayout()
        ok_button = QPushButton("创建")
        cancel_button = QPushButton("取消")

        ok_button.clicked.connect(dialog.accept)
        cancel_button.clicked.connect(dialog.reject)

        button_layout.addWidget(ok_button)
        button_layout.addWidget(cancel_button)
        layout.addLayout(button_layout)

        if dialog.exec() == QDialog.DialogCode.Accepted:
            # 创建新项目
            project_config = ProjectConfig(
                name=name_edit.text(),
                author=author_edit.text(),
                description=description_edit.toPlainText()
            )

            self.project_data = ProjectData(config=project_config)
            self.update_window_title()
            self.update_status("新项目已创建")

    def open_project(self):
        """打开项目"""
        # 检查是否需要保存当前项目
        if self.project_data and not self.check_save_current_project():
            return

        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "打开项目",
            "",
            "AI Animation Studio项目 (*.aas);;所有文件 (*)"
        )

        if file_path:
            self.load_project(file_path)

    def load_project(self, file_path: str):
        """加载项目"""
        try:
            self.update_status("正在加载项目...")
            self.show_progress(True)

            # 加载项目数据
            project_data = ProjectData.load(file_path)
            if project_data:
                self.project_data = project_data
                self.config.add_recent_project(file_path)
                self.update_window_title()
                self.update_status(f"项目已加载: {os.path.basename(file_path)}")
            else:
                QMessageBox.warning(self, "错误", "无法加载项目文件")
                self.update_status("项目加载失败")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载项目时发生错误:\n{str(e)}")
            self.update_status("项目加载失败")
        finally:
            self.show_progress(False)

    def save_project(self):
        """保存项目"""
        if not self.project_data:
            return

        if not self.project_data.project_file_path:
            self.save_project_as()
        else:
            self._save_project_to_file(self.project_data.project_file_path)

    def save_project_as(self):
        """另存为项目"""
        if not self.project_data:
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "保存项目",
            f"{self.project_data.config.name}.aas",
            "AI Animation Studio项目 (*.aas);;所有文件 (*)"
        )

        if file_path:
            self._save_project_to_file(file_path)

    def _save_project_to_file(self, file_path: str):
        """保存项目到文件"""
        try:
            self.update_status("正在保存项目...")
            self.show_progress(True)

            if self.project_data.save(file_path):
                self.config.add_recent_project(file_path)
                self.update_window_title()
                self.update_status(f"项目已保存: {os.path.basename(file_path)}")
            else:
                QMessageBox.warning(self, "错误", "保存项目失败")
                self.update_status("项目保存失败")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存项目时发生错误:\n{str(e)}")
            self.update_status("项目保存失败")
        finally:
            self.show_progress(False)

    def check_save_current_project(self) -> bool:
        """检查是否需要保存当前项目"""
        if not self.project_data:
            return True

        # 这里应该检查项目是否有未保存的更改
        # 简化实现，总是询问
        reply = QMessageBox.question(
            self,
            "保存项目",
            "当前项目有未保存的更改，是否保存？",
            QMessageBox.StandardButton.Save |
            QMessageBox.StandardButton.Discard |
            QMessageBox.StandardButton.Cancel
        )

        if reply == QMessageBox.StandardButton.Save:
            self.save_project()
            return True
        elif reply == QMessageBox.StandardButton.Discard:
            return True
        else:
            return False

    def update_recent_projects_menu(self, menu: QMenu):
        """更新最近项目菜单"""
        menu.clear()

        recent_projects = self.config.get_recent_projects()
        if not recent_projects:
            action = QAction("无最近项目", self)
            action.setEnabled(False)
            menu.addAction(action)
            return

        for project_path in recent_projects:
            action = QAction(os.path.basename(project_path), self)
            action.setStatusTip(project_path)
            action.triggered.connect(lambda checked, path=project_path: self.load_project(path))
            menu.addAction(action)

    # === 界面更新方法 ===

    def update_window_title(self):
        """更新窗口标题"""
        title = "AI Animation Studio - AI动画工作站"
        if self.project_data:
            project_name = self.project_data.config.name
            title = f"{project_name} - {title}"
            if self.project_data.project_file_path:
                title += f" - {os.path.basename(self.project_data.project_file_path)}"

        self.setWindowTitle(title)

    def update_status(self, message: str):
        """更新状态栏消息"""
        self.status_label.setText(message)

    def show_progress(self, show: bool, value: int = 0):
        """显示/隐藏进度条"""
        self.progress_bar.setVisible(show)
        if show:
            self.progress_bar.setValue(value)

    def switch_layout_mode(self, mode: LayoutMode):
        """切换布局模式"""
        self.current_layout_mode = mode

        if mode == LayoutMode.EDIT:
            # 编辑模式：舞台为主，预览为辅
            self.central_splitter.setSizes([800, 400])
            self.timeline_dock.show()
        elif mode == LayoutMode.PREVIEW:
            # 预览模式：预览为主，舞台为辅
            self.central_splitter.setSizes([400, 800])
            self.timeline_dock.hide()

        self.update_status(f"切换到{mode.value}模式")

    # === 编辑操作方法 ===

    def undo(self):
        """撤销操作"""
        self.update_status("撤销操作")
        # TODO: 实现撤销功能

    def redo(self):
        """重做操作"""
        self.update_status("重做操作")
        # TODO: 实现重做功能

    def copy(self):
        """复制操作"""
        self.update_status("复制")
        # TODO: 实现复制功能

    def paste(self):
        """粘贴操作"""
        self.update_status("粘贴")
        # TODO: 实现粘贴功能

    # === 播放控制方法 ===

    def toggle_playback(self):
        """切换播放/暂停"""
        # TODO: 实现播放控制
        if self.play_action.text() == "播放":
            self.play_action.setText("暂停")
            self.play_action.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_MediaPause))
            self.update_status("开始播放")
        else:
            self.play_action.setText("播放")
            self.play_action.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_MediaPlay))
            self.update_status("暂停播放")

    def stop_playback(self):
        """停止播放"""
        self.play_action.setText("播放")
        self.play_action.setIcon(self.style().standardIcon(QStyle.StandardPixmap.SP_MediaPlay))
        self.update_status("停止播放")
        # TODO: 实现停止播放功能

    # === 工具和对话框方法 ===

    def import_assets(self):
        """导入素材"""
        if not self.project_data:
            QMessageBox.information(self, "提示", "请先创建或打开一个项目")
            return

        file_paths, _ = QFileDialog.getOpenFileNames(
            self,
            "导入素材",
            "",
            "图片文件 (*.png *.jpg *.jpeg *.gif *.bmp *.svg);;音频文件 (*.mp3 *.wav *.ogg *.m4a);;所有文件 (*)"
        )

        if file_paths:
            self.update_status(f"正在导入 {len(file_paths)} 个文件...")
            # TODO: 实现素材导入功能
            self.update_status(f"已导入 {len(file_paths)} 个素材")

    def export_html(self):
        """导出HTML"""
        if not self.project_data:
            QMessageBox.information(self, "提示", "请先创建或打开一个项目")
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "导出HTML",
            f"{self.project_data.config.name}.html",
            "HTML文件 (*.html);;所有文件 (*)"
        )

        if file_path:
            self.update_status("正在导出HTML...")
            # TODO: 实现HTML导出功能
            self.update_status(f"HTML已导出: {os.path.basename(file_path)}")

    def export_video(self):
        """导出视频"""
        if not self.project_data:
            QMessageBox.information(self, "提示", "请先创建或打开一个项目")
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "导出视频",
            f"{self.project_data.config.name}.mp4",
            "MP4视频 (*.mp4);;WebM视频 (*.webm);;所有文件 (*)"
        )

        if file_path:
            self.update_status("正在导出视频...")
            # TODO: 实现视频导出功能
            self.update_status(f"视频已导出: {os.path.basename(file_path)}")

    def show_ai_generator(self):
        """显示AI生成器"""
        self.update_status("打开AI生成器")
        # TODO: 实现AI生成器对话框
        QMessageBox.information(self, "AI生成器", "AI生成器功能正在开发中...")

    def show_rules_editor(self):
        """显示规则编辑器"""
        self.update_status("打开规则编辑器")
        # TODO: 实现规则编辑器对话框
        QMessageBox.information(self, "规则编辑器", "规则编辑器功能正在开发中...")

    def show_settings(self):
        """显示设置对话框"""
        self.update_status("打开设置")
        # TODO: 实现设置对话框
        QMessageBox.information(self, "设置", "设置功能正在开发中...")

    def show_help(self):
        """显示帮助"""
        QMessageBox.information(
            self,
            "使用帮助",
            "AI Animation Studio - AI动画工作站\n\n"
            "这是一个基于AI技术的动画制作工具，通过自然语言描述即可生成专业动画。\n\n"
            "主要功能:\n"
            "• 旁白驱动的时间同步\n"
            "• 智能动画规则匹配\n"
            "• 多方案生成预览\n"
            "• 完美状态衔接\n"
            "• 多格式导出支持\n\n"
            "更多帮助信息请访问官方文档。"
        )

    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(
            self,
            "关于 AI Animation Studio",
            "AI Animation Studio v1.0.0\n\n"
            "AI驱动的动画工作站\n"
            "让动画创作变得简单而强大\n\n"
            "版权所有 © 2024 AI Animation Studio\n"
            "保留所有权利。"
        )

    # === 窗口事件处理 ===

    def closeEvent(self, event):
        """窗口关闭事件"""
        # 检查是否需要保存项目
        if self.project_data and not self.check_save_current_project():
            event.ignore()
            return

        # 保存窗口状态到配置
        self.config.ui.window_maximized = self.isMaximized()
        if not self.config.ui.window_maximized:
            self.config.ui.window_width = self.width()
            self.config.ui.window_height = self.height()

        # 保存配置
        self.config.save()

        event.accept()

    def resizeEvent(self, event):
        """窗口大小改变事件"""
        super().resizeEvent(event)
        # 可以在这里处理窗口大小改变的逻辑

    def keyPressEvent(self, event):
        """按键事件"""
        # 处理快捷键
        if event.key() == Qt.Key.Key_Space:
            self.toggle_playback()
            event.accept()
        elif event.key() == Qt.Key.Key_Escape:
            self.stop_playback()
            event.accept()
        else:
            super().keyPressEvent(event)
