# AI Animation Studio - 主题管理器
# 版权所有 (c) 2024 AI Animation Studio

from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QObject
from typing import Dict, Any

class ThemeManager(QObject):
    """主题管理器"""
    
    def __init__(self):
        super().__init__()
        self.themes = {
            'light': {
                'name': '浅色主题',
                'background': '#ffffff',
                'surface': '#f5f5f5',
                'primary': '#2196F3',
                'secondary': '#FFC107',
                'text': '#212121',
                'text_secondary': '#757575',
                'success': '#4CAF50',
                'warning': '#FF9800',
                'error': '#F44336',
                'border': '#E0E0E0'
            },
            'dark': {
                'name': '深色主题',
                'background': '#121212',
                'surface': '#1e1e1e',
                'primary': '#BB86FC',
                'secondary': '#03DAC6',
                'text': '#FFFFFF',
                'text_secondary': '#B0B0B0',
                'success': '#4CAF50',
                'warning': '#FF9800',
                'error': '#CF6679',
                'border': '#333333'
            },
            'blue': {
                'name': '蓝色主题',
                'background': '#f8fbff',
                'surface': '#e3f2fd',
                'primary': '#1976d2',
                'secondary': '#42a5f5',
                'text': '#0d47a1',
                'text_secondary': '#1565c0',
                'success': '#4caf50',
                'warning': '#ff9800',
                'error': '#f44336',
                'border': '#bbdefb'
            }
        }
        self.current_theme = 'light'
    
    def get_stylesheet(self, theme_name: str = None) -> str:
        """获取主题样式表"""
        if theme_name is None:
            theme_name = self.current_theme
        
        if theme_name not in self.themes:
            theme_name = 'light'
        
        theme = self.themes[theme_name]
        
        return f"""
        /* 全局样式 */
        QMainWindow {{
            background-color: {theme['background']};
            color: {theme['text']};
        }}

        QWidget {{
            background-color: {theme['background']};
            color: {theme['text']};
        }}

        /* 组框样式 */
        QGroupBox {{
            font-weight: bold;
            border: 2px solid {theme['border']};
            border-radius: 8px;
            margin-top: 1ex;
            padding-top: 8px;
            background-color: {theme['surface']};
        }}

        QGroupBox::title {{
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            color: {theme['primary']};
        }}

        /* 按钮样式 */
        QPushButton {{
            background-color: {theme['primary']};
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-weight: bold;
            min-height: 20px;
        }}

        QPushButton:hover {{
            background-color: {theme['secondary']};
        }}

        QPushButton:pressed {{
            background-color: {theme['text_secondary']};
        }}

        QPushButton:disabled {{
            background-color: {theme['border']};
            color: {theme['text_secondary']};
        }}

        /* 输入框样式 */
        QLineEdit, QTextEdit, QPlainTextEdit {{
            border: 2px solid {theme['border']};
            border-radius: 6px;
            padding: 8px;
            background-color: {theme['background']};
            color: {theme['text']};
        }}

        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {{
            border-color: {theme['primary']};
        }}

        /* 下拉框样式 */
        QComboBox {{
            border: 2px solid {theme['border']};
            border-radius: 6px;
            padding: 6px;
            background-color: {theme['background']};
            color: {theme['text']};
            min-width: 6em;
        }}

        QComboBox:focus {{
            border-color: {theme['primary']};
        }}

        QComboBox::drop-down {{
            border: none;
            width: 20px;
        }}

        QComboBox::down-arrow {{
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid {theme['text']};
        }}

        /* 进度条样式 */
        QProgressBar {{
            border: 2px solid {theme['border']};
            border-radius: 6px;
            text-align: center;
            background-color: {theme['surface']};
            color: {theme['text']};
        }}

        QProgressBar::chunk {{
            background-color: {theme['primary']};
            border-radius: 4px;
        }}

        /* 滑块样式 */
        QSlider::groove:horizontal {{
            border: 1px solid {theme['border']};
            height: 8px;
            background: {theme['surface']};
            border-radius: 4px;
        }}

        QSlider::handle:horizontal {{
            background: {theme['primary']};
            border: 2px solid {theme['primary']};
            width: 18px;
            margin: -2px 0;
            border-radius: 9px;
        }}

        QSlider::handle:horizontal:hover {{
            background: {theme['secondary']};
            border-color: {theme['secondary']};
        }}

        /* 标签页样式 */
        QTabWidget::pane {{
            border: 2px solid {theme['border']};
            border-radius: 6px;
            background-color: {theme['surface']};
        }}

        QTabBar::tab {{
            background: {theme['background']};
            border: 2px solid {theme['border']};
            padding: 8px 16px;
            margin-right: 2px;
            border-top-left-radius: 6px;
            border-top-right-radius: 6px;
        }}

        QTabBar::tab:selected {{
            background: {theme['primary']};
            color: white;
        }}

        QTabBar::tab:hover {{
            background: {theme['secondary']};
            color: white;
        }}

        /* 列表样式 */
        QListWidget, QTableWidget {{
            border: 2px solid {theme['border']};
            border-radius: 6px;
            background-color: {theme['background']};
            alternate-background-color: {theme['surface']};
        }}

        QListWidget::item, QTableWidget::item {{
            padding: 8px;
            border-bottom: 1px solid {theme['border']};
        }}

        QListWidget::item:selected, QTableWidget::item:selected {{
            background-color: {theme['primary']};
            color: white;
        }}

        QListWidget::item:hover, QTableWidget::item:hover {{
            background-color: {theme['secondary']};
            color: white;
        }}

        /* 菜单样式 */
        QMenuBar {{
            background-color: {theme['surface']};
            border-bottom: 1px solid {theme['border']};
        }}

        QMenuBar::item {{
            spacing: 3px;
            padding: 8px 12px;
            background: transparent;
            border-radius: 4px;
        }}

        QMenuBar::item:selected {{
            background: {theme['primary']};
            color: white;
        }}

        QMenu {{
            background-color: {theme['surface']};
            border: 2px solid {theme['border']};
            border-radius: 6px;
        }}

        QMenu::item {{
            padding: 8px 24px;
        }}

        QMenu::item:selected {{
            background-color: {theme['primary']};
            color: white;
        }}

        /* 状态栏样式 */
        QStatusBar {{
            background-color: {theme['surface']};
            border-top: 1px solid {theme['border']};
            color: {theme['text_secondary']};
        }}

        /* 停靠部件样式 */
        QDockWidget {{
            background-color: {theme['surface']};
            border: 1px solid {theme['border']};
            titlebar-close-icon: none;
            titlebar-normal-icon: none;
        }}

        QDockWidget::title {{
            background-color: {theme['primary']};
            color: white;
            padding: 8px;
            text-align: center;
        }}

        /* 分割器样式 */
        QSplitter::handle {{
            background-color: {theme['border']};
        }}

        QSplitter::handle:horizontal {{
            width: 3px;
        }}

        QSplitter::handle:vertical {{
            height: 3px;
        }}

        /* 滚动条样式 */
        QScrollBar:vertical {{
            background: {theme['surface']};
            width: 12px;
            border-radius: 6px;
        }}

        QScrollBar::handle:vertical {{
            background: {theme['primary']};
            border-radius: 6px;
            min-height: 20px;
        }}

        QScrollBar::handle:vertical:hover {{
            background: {theme['secondary']};
        }}

        /* 工具提示样式 */
        QToolTip {{
            background-color: {theme['text']};
            color: {theme['background']};
            border: 1px solid {theme['border']};
            border-radius: 4px;
            padding: 4px;
        }}
        """
    
    def apply_theme(self, app: QApplication, theme_name: str):
        """应用主题"""
        self.current_theme = theme_name
        stylesheet = self.get_stylesheet(theme_name)
        
        # 获取DPI缩放因子来调整样式
        screen = app.primaryScreen()
        logical_dpi = screen.logicalDotsPerInch()
        scale_factor = logical_dpi / 96.0
        
        # 调整样式表中的尺寸
        stylesheet = self._scale_stylesheet(stylesheet, scale_factor)
        
        app.setStyleSheet(stylesheet)
    
    def _scale_stylesheet(self, stylesheet: str, scale_factor: float) -> str:
        """根据DPI缩放调整样式表"""
        import re
        
        # 如果缩放因子接近1，不需要调整
        if abs(scale_factor - 1.0) < 0.1:
            return stylesheet
        
        # 调整padding值
        def scale_padding(match):
            value = int(match.group(1))
            scaled_value = max(1, int(value / scale_factor))
            return f"padding: {scaled_value}px"
        
        # 调整margin值
        def scale_margin(match):
            value = int(match.group(1))
            scaled_value = max(1, int(value / scale_factor))
            return f"margin: {scaled_value}px"
        
        # 调整border-radius值
        def scale_border_radius(match):
            value = int(match.group(1))
            scaled_value = max(1, int(value / scale_factor))
            return f"border-radius: {scaled_value}px"
        
        # 调整min-height值
        def scale_min_height(match):
            value = int(match.group(1))
            scaled_value = max(10, int(value / scale_factor))
            return f"min-height: {scaled_value}px"
        
        # 应用缩放
        stylesheet = re.sub(r'padding:\s*(\d+)px', scale_padding, stylesheet)
        stylesheet = re.sub(r'margin:\s*(\d+)px', scale_margin, stylesheet)
        stylesheet = re.sub(r'border-radius:\s*(\d+)px', scale_border_radius, stylesheet)
        stylesheet = re.sub(r'min-height:\s*(\d+)px', scale_min_height, stylesheet)
        
        return stylesheet
    
    def get_available_themes(self) -> Dict[str, str]:
        """获取可用主题列表"""
        return {key: theme['name'] for key, theme in self.themes.items()}
    
    def get_theme_colors(self, theme_name: str = None) -> Dict[str, str]:
        """获取主题颜色"""
        if theme_name is None:
            theme_name = self.current_theme
        
        return self.themes.get(theme_name, self.themes['light'])
