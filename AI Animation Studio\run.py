#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI Animation Studio - 启动脚本
版权所有 (c) 2024 AI Animation Studio
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 检查依赖
def check_dependencies():
    """检查必要的依赖"""
    missing_deps = []
    
    try:
        import PyQt6
    except ImportError:
        missing_deps.append("PyQt6")
    
    if missing_deps:
        print("缺少必要的依赖包:")
        for dep in missing_deps:
            print(f"  - {dep}")
        print("\n请安装缺少的依赖包:")
        print("pip install PyQt6")
        return False
    
    return True

def main():
    """主函数"""
    print("AI Animation Studio - AI动画工作站")
    print("版权所有 (c) 2024 AI Animation Studio")
    print("-" * 50)
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 导入并运行主程序
    try:
        from main import main as app_main
        app_main()
    except Exception as e:
        print(f"启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
