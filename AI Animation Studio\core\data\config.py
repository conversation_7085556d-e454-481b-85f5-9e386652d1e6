# AI Animation Studio - 配置管理
# 版权所有 (c) 2024 AI Animation Studio

import json
import os
from dataclasses import dataclass, asdict, field
from typing import Dict, List, Optional, Any
from pathlib import Path
from datetime import datetime

from .enums import AIModel, ThemeType, LayoutMode, QualityLevel, ExportFormat

@dataclass
class CanvasConfig:
    """画布配置"""
    width: int = 1920
    height: int = 1080
    background_color: str = "#ffffff"
    grid_enabled: bool = True
    grid_size: int = 20
    rulers_enabled: bool = True
    snap_to_grid: bool = True
    zoom_level: float = 1.0

@dataclass
class TimelineConfig:
    """时间轴配置"""
    total_duration: float = 30.0
    fps: int = 60
    time_precision: float = 0.1
    auto_save_interval: int = 300  # 秒
    waveform_height: int = 80
    segment_min_duration: float = 0.5

@dataclass
class AIConfig:
    """AI配置"""
    primary_model: AIModel = AIModel.GEMINI_FLASH
    backup_model: AIModel = AIModel.GEMINI_PRO
    api_timeout: int = 30
    max_retries: int = 3
    temperature: float = 0.7
    enable_thinking: bool = False
    gemini_api_key: str = ""
    claude_api_key: str = ""
    openai_api_key: str = ""

@dataclass
class UIConfig:
    """界面配置"""
    theme: ThemeType = ThemeType.LIGHT
    layout_mode: LayoutMode = LayoutMode.EDIT
    font_family: str = "Microsoft YaHei"
    font_size: int = 12
    window_width: int = 1400
    window_height: int = 900
    window_maximized: bool = False
    show_tooltips: bool = True
    animation_speed: float = 1.0

@dataclass
class ExportConfig:
    """导出配置"""
    default_format: ExportFormat = ExportFormat.MP4
    quality: QualityLevel = QualityLevel.HIGH
    transparent_background: bool = False
    include_audio: bool = True
    output_directory: str = "exports"
    filename_template: str = "{project_name}_{timestamp}"
    crf: int = 18  # 视频质量参数

@dataclass
class AudioConfig:
    """音频配置"""
    volume: float = 0.8
    fade_in: float = 0.5
    fade_out: float = 0.5
    sample_rate: int = 44100
    channels: int = 2
    auto_normalize: bool = True

@dataclass
class Config:
    """主配置类"""
    canvas: CanvasConfig = field(default_factory=CanvasConfig)
    timeline: TimelineConfig = field(default_factory=TimelineConfig)
    ai: AIConfig = field(default_factory=AIConfig)
    ui: UIConfig = field(default_factory=UIConfig)
    export: ExportConfig = field(default_factory=ExportConfig)
    audio: AudioConfig = field(default_factory=AudioConfig)
    
    # 应用级配置
    app_version: str = "1.0.0"
    last_project_path: str = ""
    recent_projects: List[str] = field(default_factory=list)
    auto_backup: bool = True
    backup_interval: int = 600  # 秒
    max_backups: int = 10
    
    def save(self, path: str = "config.json"):
        """保存配置到文件"""
        try:
            config_dict = asdict(self)
            # 转换枚举为字符串
            config_dict = self._serialize_enums(config_dict)
            
            with open(path, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"保存配置失败: {e}")
            return False
    
    @classmethod
    def load(cls, path: str = "config.json") -> 'Config':
        """从文件加载配置"""
        if not os.path.exists(path):
            return cls()
        
        try:
            with open(path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 反序列化枚举
            data = cls._deserialize_enums(data)
            
            # 创建配置对象
            config = cls()
            config._update_from_dict(data)
            return config
            
        except Exception as e:
            print(f"加载配置失败: {e}")
            return cls()
    
    def _serialize_enums(self, data: Dict) -> Dict:
        """序列化枚举值"""
        if isinstance(data, dict):
            result = {}
            for key, value in data.items():
                if hasattr(value, 'value'):  # 枚举类型
                    result[key] = value.value
                elif isinstance(value, dict):
                    result[key] = self._serialize_enums(value)
                else:
                    result[key] = value
            return result
        return data
    
    @classmethod
    def _deserialize_enums(cls, data: Dict) -> Dict:
        """反序列化枚举值"""
        # 枚举字段映射
        enum_fields = {
            'primary_model': AIModel,
            'backup_model': AIModel,
            'theme': ThemeType,
            'layout_mode': LayoutMode,
            'default_format': ExportFormat,
            'quality': QualityLevel
        }
        
        def convert_value(key: str, value: Any) -> Any:
            if key in enum_fields and isinstance(value, str):
                try:
                    return enum_fields[key](value)
                except ValueError:
                    # 如果枚举值无效，返回默认值
                    return list(enum_fields[key])[0]
            elif isinstance(value, dict):
                return {k: convert_value(k, v) for k, v in value.items()}
            return value
        
        return {key: convert_value(key, value) for key, value in data.items()}
    
    def _update_from_dict(self, data: Dict):
        """从字典更新配置"""
        for section_name, section_data in data.items():
            if hasattr(self, section_name) and isinstance(section_data, dict):
                section = getattr(self, section_name)
                if hasattr(section, '__dict__'):
                    for key, value in section_data.items():
                        if hasattr(section, key):
                            setattr(section, key, value)
            elif hasattr(self, section_name):
                setattr(self, section_name, section_data)
    
    def get_recent_projects(self, max_count: int = 10) -> List[str]:
        """获取最近项目列表"""
        # 过滤不存在的项目
        existing_projects = [p for p in self.recent_projects if os.path.exists(p)]
        self.recent_projects = existing_projects[:max_count]
        return self.recent_projects
    
    def add_recent_project(self, project_path: str):
        """添加最近项目"""
        if project_path in self.recent_projects:
            self.recent_projects.remove(project_path)
        self.recent_projects.insert(0, project_path)
        self.recent_projects = self.recent_projects[:10]  # 保持最多10个
    
    def validate(self) -> List[str]:
        """验证配置有效性"""
        issues = []
        
        # 验证画布配置
        if self.canvas.width < 100 or self.canvas.width > 7680:
            issues.append("画布宽度超出有效范围 (100-7680)")
        if self.canvas.height < 100 or self.canvas.height > 4320:
            issues.append("画布高度超出有效范围 (100-4320)")
        
        # 验证时间轴配置
        if self.timeline.total_duration <= 0:
            issues.append("动画总时长必须大于0")
        if self.timeline.fps < 1 or self.timeline.fps > 120:
            issues.append("帧率超出有效范围 (1-120)")
        
        # 验证AI配置
        if not self.ai.gemini_api_key and self.ai.primary_model == AIModel.GEMINI_FLASH:
            issues.append("使用Gemini模型需要设置API密钥")
        
        # 验证音频配置
        if self.audio.volume < 0 or self.audio.volume > 1:
            issues.append("音频音量超出有效范围 (0-1)")
        
        return issues

@dataclass
class ProjectConfig:
    """项目配置"""
    name: str = "新建项目"
    description: str = ""
    author: str = ""
    version: str = "1.0.0"
    created_at: str = field(default_factory=lambda: datetime.now().isoformat())
    modified_at: str = field(default_factory=lambda: datetime.now().isoformat())
    
    # 项目特定配置
    canvas_config: CanvasConfig = field(default_factory=CanvasConfig)
    timeline_config: TimelineConfig = field(default_factory=TimelineConfig)
    export_config: ExportConfig = field(default_factory=ExportConfig)
    
    # 项目文件路径
    project_file: str = ""
    assets_directory: str = "assets"
    exports_directory: str = "exports"
    backups_directory: str = "backups"
    
    def save(self, path: str):
        """保存项目配置"""
        try:
            self.modified_at = datetime.now().isoformat()
            with open(path, 'w', encoding='utf-8') as f:
                json.dump(asdict(self), f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"保存项目配置失败: {e}")
            return False
    
    @classmethod
    def load(cls, path: str) -> 'ProjectConfig':
        """加载项目配置"""
        try:
            with open(path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 创建配置对象
            config = cls()
            for key, value in data.items():
                if hasattr(config, key):
                    setattr(config, key, value)
            
            return config
        except Exception as e:
            print(f"加载项目配置失败: {e}")
            return cls()
    
    def get_project_directory(self) -> str:
        """获取项目目录"""
        if self.project_file:
            return os.path.dirname(self.project_file)
        return ""
    
    def get_assets_path(self) -> str:
        """获取素材目录路径"""
        project_dir = self.get_project_directory()
        if project_dir:
            return os.path.join(project_dir, self.assets_directory)
        return self.assets_directory
    
    def get_exports_path(self) -> str:
        """获取导出目录路径"""
        project_dir = self.get_project_directory()
        if project_dir:
            return os.path.join(project_dir, self.exports_directory)
        return self.exports_directory
