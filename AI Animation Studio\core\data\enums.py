# AI Animation Studio - 枚举定义
# 版权所有 (c) 2024 AI Animation Studio

from enum import Enum, auto

class AnimationType(Enum):
    """动画类型枚举"""
    APPEAR = "appear"           # 出现动画
    DISAPPEAR = "disappear"     # 消失动画
    MOVE = "move"              # 移动动画
    TRANSFORM = "transform"     # 变换动画
    EMPHASIS = "emphasis"       # 强调动画
    TRANSITION = "transition"   # 转场动画
    INTERACTIVE = "interactive" # 交互动画
    MIXED = "mixed"            # 混合动画

class ElementType(Enum):
    """元素类型枚举"""
    TEXT = "text"              # 文本元素
    IMAGE = "image"            # 图片元素
    SVG = "svg"               # SVG图形
    SHAPE = "shape"           # 几何图形
    CHART = "chart"           # 图表元素
    VIDEO = "video"           # 视频元素
    AUDIO = "audio"           # 音频元素
    GROUP = "group"           # 组合元素

class PathType(Enum):
    """路径类型枚举"""
    LINEAR = "linear"          # 直线路径
    BEZIER = "bezier"         # 贝塞尔曲线
    ARC = "arc"               # 弧线路径
    SPIRAL = "spiral"         # 螺旋路径
    WAVE = "wave"             # 波浪路径
    BOUNCE = "bounce"         # 弹跳路径
    CUSTOM = "custom"         # 自定义路径
    DRAG_TRACE = "drag_trace" # 拖拽轨迹

class ExportFormat(Enum):
    """导出格式枚举"""
    HTML = "html"             # HTML文件
    MP4 = "mp4"              # MP4视频
    WEBM = "webm"            # WebM视频
    GIF = "gif"              # GIF动图
    PNG_SEQUENCE = "png_seq"  # PNG序列
    JSON = "json"            # JSON数据

class AIModel(Enum):
    """AI模型枚举"""
    GEMINI_FLASH = "gemini-2.5-flash"
    GEMINI_PRO = "gemini-pro"
    CLAUDE_SONNET = "claude-3.5-sonnet"
    GPT4 = "gpt-4"
    LOCAL_MODEL = "local"

class ThemeType(Enum):
    """主题类型枚举"""
    LIGHT = "light"           # 浅色主题
    DARK = "dark"            # 深色主题
    BLUE = "blue"            # 蓝色主题
    GREEN = "green"          # 绿色主题
    PURPLE = "purple"        # 紫色主题

class LayoutMode(Enum):
    """布局模式枚举"""
    EDIT = "edit"            # 编辑模式
    PREVIEW = "preview"      # 预览模式
    SPLIT = "split"          # 分割模式

class QualityLevel(Enum):
    """质量等级枚举"""
    LOW = "low"              # 低质量
    MEDIUM = "medium"        # 中等质量
    HIGH = "high"            # 高质量
    ULTRA = "ultra"          # 超高质量

class StateType(Enum):
    """状态类型枚举"""
    INITIAL = "initial"      # 初始状态
    INTERMEDIATE = "intermediate"  # 中间状态
    FINAL = "final"          # 最终状态
    TRANSITION = "transition" # 过渡状态

class EventType(Enum):
    """事件类型枚举"""
    CLICK = "click"          # 点击事件
    HOVER = "hover"          # 悬停事件
    SCROLL = "scroll"        # 滚动事件
    KEYPRESS = "keypress"    # 按键事件
    LOAD = "load"            # 加载事件
    RESIZE = "resize"        # 调整大小事件

class ValidationLevel(Enum):
    """验证级别枚举"""
    NONE = "none"            # 无验证
    BASIC = "basic"          # 基础验证
    STRICT = "strict"        # 严格验证
    COMPREHENSIVE = "comprehensive"  # 全面验证

class LogLevel(Enum):
    """日志级别枚举"""
    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"

class OperationType(Enum):
    """操作类型枚举"""
    CREATE = "create"        # 创建操作
    UPDATE = "update"        # 更新操作
    DELETE = "delete"        # 删除操作
    MOVE = "move"           # 移动操作
    COPY = "copy"           # 复制操作
    PASTE = "paste"         # 粘贴操作
    UNDO = "undo"           # 撤销操作
    REDO = "redo"           # 重做操作
