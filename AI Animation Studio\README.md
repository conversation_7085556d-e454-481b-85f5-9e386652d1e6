# AI Animation Studio - AI动画工作站

## 项目简介

AI Animation Studio 是一个基于人工智能技术的动画制作工具，通过自然语言描述即可生成专业级的Web动画。本软件专为科普内容创作者、教育工作者和动画爱好者设计，让复杂的动画制作变得简单而高效。

## 核心特性

### 🎯 旁白驱动制作
- 通过旁白音频精确控制动画节奏
- 自动时间同步，确保动画与解说完美配合
- 可视化时间轴，直观管理动画时间段

### 🤖 AI智能生成
- 自然语言描述转换为专业动画代码
- 多种AI模型支持（Gemini、Claude、GPT等）
- 智能动画规则匹配，自动选择最佳技术方案
- 一次描述生成多种风格方案供选择

### 🎨 完美状态衔接
- 智能状态管理，确保动画流畅连接
- 实时状态快照和冲突检测
- 自动修复状态不一致问题

### 🛠️ 混合编辑模式
- AI生成 + 手动微调的完美结合
- 支持CSS、GSAP、Three.js等多种技术
- 可视化路径绘制和元素编辑

### 📤 多格式导出
- HTML动画文件导出
- 高质量MP4/WebM视频渲染
- 透明背景支持，便于后期合成
- PNG序列帧导出

## 系统要求

- **操作系统**: Windows 10/11, macOS 10.15+, Linux (Ubuntu 18.04+)
- **Python**: 3.8 或更高版本
- **内存**: 建议 8GB 或以上
- **显卡**: 支持 OpenGL 3.3 或更高版本
- **网络**: 需要互联网连接以使用AI服务

## 安装指南

### 1. 克隆项目
```bash
git clone https://github.com/ai-animation-studio/ai-animation-studio.git
cd ai-animation-studio
```

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 配置API密钥
在项目根目录创建 `config.json` 文件，配置AI服务API密钥：

```json
{
  "ai": {
    "gemini_api_key": "your_gemini_api_key_here",
    "claude_api_key": "your_claude_api_key_here",
    "openai_api_key": "your_openai_api_key_here"
  }
}
```

### 4. 运行程序
```bash
python run.py
```

## 快速开始

### 创建第一个动画项目

1. **新建项目**
   - 启动软件后，点击"文件" → "新建项目"
   - 填写项目名称、作者等基本信息

2. **导入旁白音频**
   - 在时间轴面板点击"导入音频"
   - 选择您的旁白音频文件（支持MP3、WAV等格式）

3. **添加动画元素**
   - 在舞台面板拖拽添加文字、图片或图形元素
   - 设置元素的初始位置和样式

4. **标记时间段**
   - 播放旁白音频，在关键时刻暂停
   - 点击"标记时间段"创建动画片段
   - 为时间段添加描述

5. **AI生成动画**
   - 选择时间段，点击"AI生成"
   - 用自然语言描述动画效果，如"小球从左到右弹跳移动"
   - 选择喜欢的生成方案

6. **预览和调整**
   - 在预览面板查看动画效果
   - 根据需要进行微调

7. **导出作品**
   - 点击"文件" → "导出"
   - 选择导出格式（HTML或视频）

## 项目结构

```
AI Animation Studio/
├── main.py                 # 主程序入口
├── run.py                  # 启动脚本
├── requirements.txt        # 依赖包列表
├── README.md              # 项目说明
├── core/                  # 核心模块
│   ├── data/             # 数据结构
│   ├── ai/               # AI服务
│   └── animation/        # 动画处理
├── ui/                   # 用户界面
│   ├── widgets/          # 界面组件
│   ├── dialogs/          # 对话框
│   └── themes/           # 主题管理
├── assets/               # 资源文件
│   ├── icons/            # 图标
│   ├── templates/        # 模板
│   └── rules/            # 动画规则
├── docs/                 # 文档
├── tests/                # 测试文件
└── examples/             # 示例项目
```

## 开发状态

当前版本: **v1.0.0-alpha**

### 已完成功能
- ✅ 基础项目架构
- ✅ 核心数据结构
- ✅ 主界面框架
- ✅ 主题系统
- ✅ 项目管理

### 开发中功能
- 🚧 时间轴系统
- 🚧 舞台编辑器
- 🚧 AI生成引擎
- 🚧 动画预览
- 🚧 状态管理
- 🚧 导出系统

### 计划功能
- 📋 动画规则库
- 📋 素材管理
- 📋 协作功能
- 📋 插件系统

## 贡献指南

我们欢迎社区贡献！请查看 [CONTRIBUTING.md](CONTRIBUTING.md) 了解如何参与项目开发。

### 开发环境设置

1. Fork 项目到您的GitHub账户
2. 克隆您的Fork到本地
3. 创建新的功能分支
4. 进行开发和测试
5. 提交Pull Request

## 许可证

本项目采用 MIT 许可证。详情请查看 [LICENSE](LICENSE) 文件。

## 联系我们

- **项目主页**: https://github.com/ai-animation-studio/ai-animation-studio
- **问题反馈**: https://github.com/ai-animation-studio/ai-animation-studio/issues
- **邮箱**: <EMAIL>

## 致谢

感谢以下开源项目和服务：

- [PyQt6](https://www.riverbankcomputing.com/software/pyqt/) - 跨平台GUI框架
- [Google Gemini](https://ai.google.dev/) - AI生成服务
- [GSAP](https://greensock.com/gsap/) - 高性能动画库
- [Three.js](https://threejs.org/) - 3D图形库

---

**AI Animation Studio** - 让动画创作变得简单而强大 ✨
