# AI Animation Studio - 项目数据管理
# 版权所有 (c) 2024 AI Animation Studio

import json
import os
import shutil
import uuid
from dataclasses import dataclass, field, asdict
from typing import Dict, List, Optional, Any
from pathlib import Path
from datetime import datetime

from .config import ProjectConfig
from .animation_state import AnimationState, TimeSegment, ElementState
from .enums import ElementType, ExportFormat

@dataclass
class AssetInfo:
    """素材信息"""
    asset_id: str
    name: str
    file_path: str
    asset_type: str  # image, svg, audio, video, font
    file_size: int
    created_at: str = field(default_factory=lambda: datetime.now().isoformat())
    tags: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def get_absolute_path(self, project_dir: str) -> str:
        """获取绝对路径"""
        if os.path.isabs(self.file_path):
            return self.file_path
        return os.path.join(project_dir, self.file_path)

@dataclass
class ElementInfo:
    """元素信息"""
    element_id: str
    name: str
    element_type: ElementType
    asset_id: Optional[str] = None  # 关联的素材ID
    
    # 基础属性
    initial_position: tuple = (0, 0)
    initial_size: tuple = (100, 100)
    initial_rotation: float = 0.0
    initial_opacity: float = 1.0
    
    # 样式属性
    color: str = "#000000"
    background_color: str = "transparent"
    border_style: str = "none"
    font_family: str = "Arial"
    font_size: str = "16px"
    
    # 元数据
    created_at: str = field(default_factory=lambda: datetime.now().isoformat())
    modified_at: str = field(default_factory=lambda: datetime.now().isoformat())
    tags: List[str] = field(default_factory=list)
    notes: str = ""
    
    # 自定义属性
    custom_properties: Dict[str, Any] = field(default_factory=dict)
    
    def update_modified_time(self):
        """更新修改时间"""
        self.modified_at = datetime.now().isoformat()

@dataclass
class ExportInfo:
    """导出信息"""
    export_id: str
    name: str
    format: ExportFormat
    file_path: str
    file_size: int
    duration: float
    resolution: tuple  # (width, height)
    quality_settings: Dict[str, Any]
    exported_at: str = field(default_factory=lambda: datetime.now().isoformat())
    notes: str = ""

class AssetManager:
    """素材管理器"""
    
    def __init__(self, project_dir: str):
        self.project_dir = project_dir
        self.assets_dir = os.path.join(project_dir, "assets")
        self.assets: Dict[str, AssetInfo] = {}
        
        # 确保素材目录存在
        os.makedirs(self.assets_dir, exist_ok=True)
        
        # 创建子目录
        for subdir in ["images", "audio", "fonts", "generated"]:
            os.makedirs(os.path.join(self.assets_dir, subdir), exist_ok=True)
    
    def import_asset(self, file_path: str, name: str = None, tags: List[str] = None) -> Optional[AssetInfo]:
        """导入素材"""
        if not os.path.exists(file_path):
            return None
        
        # 生成素材ID和名称
        asset_id = str(uuid.uuid4())
        if name is None:
            name = os.path.splitext(os.path.basename(file_path))[0]
        
        # 确定素材类型
        ext = os.path.splitext(file_path)[1].lower()
        asset_type = self._get_asset_type(ext)
        
        # 确定目标目录
        target_dir = os.path.join(self.assets_dir, self._get_asset_subdir(asset_type))
        target_filename = f"{asset_id}{ext}"
        target_path = os.path.join(target_dir, target_filename)
        
        try:
            # 复制文件
            shutil.copy2(file_path, target_path)
            
            # 创建素材信息
            asset_info = AssetInfo(
                asset_id=asset_id,
                name=name,
                file_path=os.path.relpath(target_path, self.project_dir),
                asset_type=asset_type,
                file_size=os.path.getsize(target_path),
                tags=tags or [],
                metadata=self._extract_metadata(target_path, asset_type)
            )
            
            self.assets[asset_id] = asset_info
            return asset_info
            
        except Exception as e:
            print(f"导入素材失败: {e}")
            return None
    
    def remove_asset(self, asset_id: str) -> bool:
        """移除素材"""
        if asset_id not in self.assets:
            return False
        
        asset_info = self.assets[asset_id]
        file_path = asset_info.get_absolute_path(self.project_dir)
        
        try:
            # 删除文件
            if os.path.exists(file_path):
                os.remove(file_path)
            
            # 从管理器中移除
            del self.assets[asset_id]
            return True
            
        except Exception as e:
            print(f"移除素材失败: {e}")
            return False
    
    def get_asset(self, asset_id: str) -> Optional[AssetInfo]:
        """获取素材信息"""
        return self.assets.get(asset_id)
    
    def get_assets_by_type(self, asset_type: str) -> List[AssetInfo]:
        """按类型获取素材"""
        return [asset for asset in self.assets.values() if asset.asset_type == asset_type]
    
    def search_assets(self, query: str) -> List[AssetInfo]:
        """搜索素材"""
        query = query.lower()
        results = []
        
        for asset in self.assets.values():
            if (query in asset.name.lower() or 
                any(query in tag.lower() for tag in asset.tags)):
                results.append(asset)
        
        return results
    
    def _get_asset_type(self, ext: str) -> str:
        """根据扩展名确定素材类型"""
        image_exts = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'}
        audio_exts = {'.mp3', '.wav', '.ogg', '.m4a', '.flac'}
        video_exts = {'.mp4', '.webm', '.avi', '.mov', '.mkv'}
        font_exts = {'.ttf', '.otf', '.woff', '.woff2'}
        
        if ext in image_exts:
            return "image"
        elif ext == '.svg':
            return "svg"
        elif ext in audio_exts:
            return "audio"
        elif ext in video_exts:
            return "video"
        elif ext in font_exts:
            return "font"
        else:
            return "other"
    
    def _get_asset_subdir(self, asset_type: str) -> str:
        """获取素材子目录"""
        mapping = {
            "image": "images",
            "svg": "images", 
            "audio": "audio",
            "video": "images",
            "font": "fonts",
            "other": "generated"
        }
        return mapping.get(asset_type, "generated")
    
    def _extract_metadata(self, file_path: str, asset_type: str) -> Dict[str, Any]:
        """提取素材元数据"""
        metadata = {}
        
        try:
            if asset_type in ["image", "svg"]:
                # 尝试获取图片尺寸
                from PIL import Image
                with Image.open(file_path) as img:
                    metadata["width"] = img.width
                    metadata["height"] = img.height
                    metadata["format"] = img.format
        except ImportError:
            pass
        except Exception:
            pass
        
        return metadata
    
    def save_manifest(self) -> bool:
        """保存素材清单"""
        manifest_path = os.path.join(self.assets_dir, "manifest.json")
        try:
            manifest_data = {
                "assets": {aid: asdict(asset) for aid, asset in self.assets.items()},
                "updated_at": datetime.now().isoformat()
            }
            
            with open(manifest_path, 'w', encoding='utf-8') as f:
                json.dump(manifest_data, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"保存素材清单失败: {e}")
            return False
    
    def load_manifest(self) -> bool:
        """加载素材清单"""
        manifest_path = os.path.join(self.assets_dir, "manifest.json")
        if not os.path.exists(manifest_path):
            return True  # 没有清单文件不算错误
        
        try:
            with open(manifest_path, 'r', encoding='utf-8') as f:
                manifest_data = json.load(f)
            
            self.assets.clear()
            for asset_id, asset_data in manifest_data.get("assets", {}).items():
                self.assets[asset_id] = AssetInfo(**asset_data)
            
            return True
        except Exception as e:
            print(f"加载素材清单失败: {e}")
            return False

@dataclass
class ProjectData:
    """项目数据"""
    config: ProjectConfig = field(default_factory=ProjectConfig)
    animation_state: AnimationState = field(default_factory=lambda: AnimationState(""))
    elements: Dict[str, ElementInfo] = field(default_factory=dict)
    exports: Dict[str, ExportInfo] = field(default_factory=dict)
    
    # 项目文件路径
    project_file_path: str = ""
    
    # 素材管理器
    asset_manager: Optional[AssetManager] = None
    
    def __post_init__(self):
        """初始化后处理"""
        if self.project_file_path:
            project_dir = os.path.dirname(self.project_file_path)
            self.asset_manager = AssetManager(project_dir)
            self.asset_manager.load_manifest()
    
    def create_element(self, name: str, element_type: ElementType, **kwargs) -> ElementInfo:
        """创建元素"""
        element_id = str(uuid.uuid4())
        element = ElementInfo(
            element_id=element_id,
            name=name,
            element_type=element_type,
            **kwargs
        )
        
        self.elements[element_id] = element
        return element
    
    def remove_element(self, element_id: str) -> bool:
        """移除元素"""
        if element_id not in self.elements:
            return False
        
        # 从所有时间段中移除该元素
        for segment in self.animation_state.segments.values():
            segment.remove_element(element_id)
        
        # 从元素列表中移除
        del self.elements[element_id]
        return True
    
    def get_element(self, element_id: str) -> Optional[ElementInfo]:
        """获取元素"""
        return self.elements.get(element_id)
    
    def save(self, file_path: str = None) -> bool:
        """保存项目"""
        if file_path is None:
            file_path = self.project_file_path
        
        if not file_path:
            return False
        
        try:
            # 更新配置
            self.config.project_file = file_path
            self.config.modified_at = datetime.now().isoformat()
            
            # 准备保存数据
            save_data = {
                "config": asdict(self.config),
                "animation_state": asdict(self.animation_state),
                "elements": {eid: asdict(element) for eid, element in self.elements.items()},
                "exports": {eid: asdict(export) for eid, export in self.exports.items()},
                "version": "1.0.0",
                "saved_at": datetime.now().isoformat()
            }
            
            # 保存到文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, indent=2, ensure_ascii=False)
            
            # 保存素材清单
            if self.asset_manager:
                self.asset_manager.save_manifest()
            
            self.project_file_path = file_path
            return True
            
        except Exception as e:
            print(f"保存项目失败: {e}")
            return False
    
    @classmethod
    def load(cls, file_path: str) -> Optional['ProjectData']:
        """加载项目"""
        if not os.path.exists(file_path):
            return None
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 创建项目数据对象
            project = cls()
            project.project_file_path = file_path
            
            # 加载配置
            if "config" in data:
                project.config = ProjectConfig(**data["config"])
            
            # 加载动画状态
            if "animation_state" in data:
                state_data = data["animation_state"]
                project.animation_state = AnimationState(**state_data)
            
            # 加载元素
            if "elements" in data:
                for element_id, element_data in data["elements"].items():
                    project.elements[element_id] = ElementInfo(**element_data)
            
            # 加载导出信息
            if "exports" in data:
                for export_id, export_data in data["exports"].items():
                    project.exports[export_id] = ExportInfo(**export_data)
            
            # 初始化素材管理器
            project_dir = os.path.dirname(file_path)
            project.asset_manager = AssetManager(project_dir)
            project.asset_manager.load_manifest()
            
            return project
            
        except Exception as e:
            print(f"加载项目失败: {e}")
            return None
    
    def create_backup(self) -> bool:
        """创建备份"""
        if not self.project_file_path:
            return False
        
        try:
            project_dir = os.path.dirname(self.project_file_path)
            backup_dir = os.path.join(project_dir, "backups")
            os.makedirs(backup_dir, exist_ok=True)
            
            # 生成备份文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"backup_{timestamp}.json"
            backup_path = os.path.join(backup_dir, backup_name)
            
            # 保存备份
            return self.save(backup_path)
            
        except Exception as e:
            print(f"创建备份失败: {e}")
            return False
    
    def validate(self) -> List[str]:
        """验证项目数据"""
        issues = []
        
        # 验证配置
        config_issues = self.config.validate() if hasattr(self.config, 'validate') else []
        issues.extend(config_issues)
        
        # 验证动画状态
        state_issues = self.animation_state.validate_continuity()
        issues.extend(state_issues)
        
        # 验证元素
        for element_id, element in self.elements.items():
            if not element.name.strip():
                issues.append(f"元素 {element_id} 名称为空")
        
        return issues
