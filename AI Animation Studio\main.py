#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI Animation Studio - 主程序入口
版权所有 (c) 2024 AI Animation Studio
作者: AI Assistant
描述: AI驱动的动画工作站主程序
"""

import sys
import os
import traceback
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# PyQt6 imports
from PyQt6.QtWidgets import QApplication, QMessageBox, QSplashScreen
from PyQt6.QtCore import Qt, QTimer, QTranslator, QLocale
from PyQt6.QtGui import QPixmap, QFont, QIcon

# 项目模块
from ui.main_window import MainWindow
from core.data.config import Config
from ui.themes.theme_manager import ThemeManager

class AIAnimationStudioApp(QApplication):
    """AI动画工作站应用程序类"""
    
    def __init__(self, argv):
        super().__init__(argv)
        
        # 应用程序信息
        self.setApplicationName("AI Animation Studio")
        self.setApplicationVersion("1.0.0")
        self.setApplicationDisplayName("AI动画工作站")
        self.setOrganizationName("AI Animation Studio")
        self.setOrganizationDomain("ai-animation-studio.com")
        
        # 设置应用程序图标
        self.setWindowIcon(QIcon("assets/icons/app_icon.png"))
        
        # 初始化组件
        self.config = None
        self.theme_manager = None
        self.main_window = None
        self.splash = None
        
        # 设置异常处理
        sys.excepthook = self.handle_exception
        
    def initialize(self):
        """初始化应用程序"""
        try:
            # 显示启动画面
            self.show_splash_screen()
            
            # 加载配置
            self.load_configuration()
            
            # 初始化主题管理器
            self.initialize_theme_manager()
            
            # 设置字体
            self.setup_fonts()
            
            # 创建主窗口
            self.create_main_window()
            
            # 应用主题
            self.apply_theme()
            
            # 隐藏启动画面并显示主窗口
            self.finalize_startup()
            
            return True
            
        except Exception as e:
            self.handle_startup_error(e)
            return False
    
    def show_splash_screen(self):
        """显示启动画面"""
        try:
            # 创建启动画面
            splash_pixmap = QPixmap(400, 300)
            splash_pixmap.fill(Qt.GlobalColor.white)
            
            self.splash = QSplashScreen(splash_pixmap)
            self.splash.setWindowFlags(Qt.WindowType.WindowStaysOnTopHint | Qt.WindowType.FramelessWindowHint)
            
            # 显示启动信息
            self.splash.showMessage(
                "正在启动 AI Animation Studio...",
                Qt.AlignmentFlag.AlignBottom | Qt.AlignmentFlag.AlignCenter,
                Qt.GlobalColor.black
            )
            
            self.splash.show()
            self.processEvents()
            
        except Exception as e:
            print(f"启动画面创建失败: {e}")
    
    def load_configuration(self):
        """加载配置"""
        try:
            self.update_splash_message("正在加载配置...")
            
            # 加载应用配置
            config_path = "config.json"
            self.config = Config.load(config_path)
            
            # 验证配置
            issues = self.config.validate()
            if issues:
                print("配置验证警告:")
                for issue in issues:
                    print(f"  - {issue}")
            
        except Exception as e:
            print(f"配置加载失败: {e}")
            self.config = Config()  # 使用默认配置
    
    def initialize_theme_manager(self):
        """初始化主题管理器"""
        try:
            self.update_splash_message("正在初始化主题...")
            
            from ui.themes.theme_manager import ThemeManager
            self.theme_manager = ThemeManager()
            
        except Exception as e:
            print(f"主题管理器初始化失败: {e}")
    
    def setup_fonts(self):
        """设置字体"""
        try:
            self.update_splash_message("正在设置字体...")
            
            # 设置默认字体
            font = QFont(self.config.ui.font_family, self.config.ui.font_size)
            self.setFont(font)
            
        except Exception as e:
            print(f"字体设置失败: {e}")
    
    def create_main_window(self):
        """创建主窗口"""
        try:
            self.update_splash_message("正在创建主界面...")
            
            self.main_window = MainWindow(self.config)
            
            # 设置窗口属性
            if self.config.ui.window_maximized:
                self.main_window.showMaximized()
            else:
                self.main_window.resize(
                    self.config.ui.window_width,
                    self.config.ui.window_height
                )
            
        except Exception as e:
            print(f"主窗口创建失败: {e}")
            raise
    
    def apply_theme(self):
        """应用主题"""
        try:
            if self.theme_manager:
                self.update_splash_message("正在应用主题...")
                self.theme_manager.apply_theme(self, self.config.ui.theme.value)
            
        except Exception as e:
            print(f"主题应用失败: {e}")
    
    def finalize_startup(self):
        """完成启动"""
        try:
            self.update_splash_message("启动完成!")
            
            # 延迟显示主窗口
            QTimer.singleShot(500, self._show_main_window)
            
        except Exception as e:
            print(f"启动完成失败: {e}")
    
    def _show_main_window(self):
        """显示主窗口"""
        if self.splash:
            self.splash.close()
        
        if self.main_window:
            self.main_window.show()
            self.main_window.raise_()
            self.main_window.activateWindow()
    
    def update_splash_message(self, message: str):
        """更新启动画面消息"""
        if self.splash:
            self.splash.showMessage(
                message,
                Qt.AlignmentFlag.AlignBottom | Qt.AlignmentFlag.AlignCenter,
                Qt.GlobalColor.black
            )
            self.processEvents()
    
    def handle_startup_error(self, error: Exception):
        """处理启动错误"""
        error_msg = f"应用程序启动失败:\n\n{str(error)}\n\n详细信息:\n{traceback.format_exc()}"
        
        if self.splash:
            self.splash.close()
        
        QMessageBox.critical(
            None,
            "启动错误",
            error_msg
        )
    
    def handle_exception(self, exc_type, exc_value, exc_traceback):
        """全局异常处理"""
        if issubclass(exc_type, KeyboardInterrupt):
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        
        error_msg = f"发生未处理的异常:\n\n{''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))}"
        
        print(error_msg)
        
        # 显示错误对话框
        QMessageBox.critical(
            self.main_window if self.main_window else None,
            "程序错误",
            f"程序遇到错误:\n\n{exc_value}\n\n请查看控制台获取详细信息。"
        )
    
    def closeEvent(self, event):
        """应用程序关闭事件"""
        try:
            # 保存配置
            if self.config and self.main_window:
                # 保存窗口状态
                self.config.ui.window_maximized = self.main_window.isMaximized()
                if not self.config.ui.window_maximized:
                    self.config.ui.window_width = self.main_window.width()
                    self.config.ui.window_height = self.main_window.height()
                
                # 保存配置文件
                self.config.save()
            
            # 关闭主窗口
            if self.main_window:
                self.main_window.close()
            
        except Exception as e:
            print(f"应用程序关闭时发生错误: {e}")
        
        event.accept()

def main():
    """主函数"""
    # 设置高DPI支持
    QApplication.setHighDpiScaleFactorRoundingPolicy(
        Qt.HighDpiScaleFactorRoundingPolicy.PassThrough
    )
    QApplication.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling, True)
    QApplication.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps, True)
    
    # 创建应用程序
    app = AIAnimationStudioApp(sys.argv)
    
    # 初始化应用程序
    if not app.initialize():
        sys.exit(1)
    
    # 运行应用程序
    try:
        exit_code = app.exec()
        sys.exit(exit_code)
    except Exception as e:
        print(f"应用程序运行时发生错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
