import sys
import os
import json
import subprocess
import threading
import time
from datetime import datetime
from pathlib import Path
from dataclasses import dataclass, asdict
from enum import Enum
from typing import Dict, List, Optional
import re
import tempfile
import webbrowser

# Qt imports
from PyQt6.QtWidgets import *
from PyQt6.QtCore import QThread, pyqtSignal, QTimer, QUrl, Qt
from PyQt6.QtGui import QPixmap, QFont, QKeySequence, QShortcut
from PyQt6.QtWebEngineWidgets import QWebEngineView
from PyQt6.QtWebEngineCore import QWebEngineSettings, QWebEngineProfile

# Third party imports
try:
    from playwright.sync_api import sync_playwright
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False

try:
    import psutil
except ImportError:
    psutil = None

# Gemini API
try:
    from google import genai
    from google.genai import types
    GEMINI_AVAILABLE = True
except ImportError:
    GEMINI_AVAILABLE = False

# === 核心数据结构 ===
class AnimationType(Enum):
    CSS_ANIMATION = "css_animation"
    GSAP = "gsap"
    THREE_JS = "three_js"
    JAVASCRIPT = "javascript"
    MIXED = "mixed"
    INTERACTIVE_CLICK = "interactive_click"      # 新增
    INTERACTIVE_SCROLL = "interactive_scroll"    # 新增
    INTERACTIVE_HOVER = "interactive_hover"      # 新增
    INTERACTIVE_MIXED = "interactive_mixed"      # 新增
    UNKNOWN = "unknown"

@dataclass
class InteractionEvent:
    timestamp: float
    event_type: str  # 'click', 'scroll', 'hover', 'keypress'
    target_element: str  # CSS选择器
    position: Dict[str, int]  # {'x': 100, 'y': 200}
    scroll_position: Dict[str, int]  # {'scrollX': 0, 'scrollY': 500}
    additional_data: Dict = None

@dataclass
class Config:
    width: int = 1920
    height: int = 1080
    fps: int = 60
    total_frames: int = 600
    crf: int = 18
    html_file: str = ""
    output_path: str = "output.mp4"
    gemini_api_key: str = ""
    gemini_model: str = "gemini-2.5-flash"  # 新增：模型选择
    enable_thinking: bool = False  # 新增：深度思考开关

    # 新增交互式录制配置
    animation_type: str = "time_based"  # 新增
    interaction_file: str = ""          # 新增
    enable_interaction_recording: bool = True  # 新增
    auto_detect_interactions: bool = True      # 新增

    def save(self, path="config.json"):
        with open(path, 'w', encoding='utf-8') as f:
            json.dump(asdict(self), f, indent=2, ensure_ascii=False)
    
    @classmethod
    def load(cls, path="config.json"):
        if os.path.exists(path):
            try:
                with open(path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    # 兼容旧版本配置
                    if 'gemini_model' not in data:
                        data['gemini_model'] = "gemini-2.5-flash"
                    if 'enable_thinking' not in data:
                        data['enable_thinking'] = False
                    # 新增交互式录制配置的兼容性
                    if 'animation_type' not in data:
                        data['animation_type'] = "time_based"
                    if 'interaction_file' not in data:
                        data['interaction_file'] = ""
                    if 'enable_interaction_recording' not in data:
                        data['enable_interaction_recording'] = True
                    if 'auto_detect_interactions' not in data:
                        data['auto_detect_interactions'] = True
                    return cls(**data)
            except (json.JSONDecodeError, TypeError):
                return cls()
        return cls()

@dataclass
class DetectionResult:
    type: AnimationType
    confidence: float
    suggestions: List[str]
    control_code: str = ""

# === Gemini生成器 ===
class GeminiGenerator(QThread):
    """Gemini HTML动画生成器"""
    
    result_ready = pyqtSignal(str)  # 生成的HTML
    error_occurred = pyqtSignal(str)  # 错误信息
    progress_update = pyqtSignal(str)  # 进度更新
    
    def __init__(self, api_key: str, prompt: str, animation_type: str, model: str, enable_thinking: bool):
        super().__init__()
        self.api_key = api_key
        self.prompt = prompt
        self.animation_type = animation_type
        self.model = model
        self.enable_thinking = enable_thinking
        
    def run(self):
        try:
            if not GEMINI_AVAILABLE:
                self.error_occurred.emit("Gemini库未安装，请运行: pip install google-generativeai")
                return
                
            if not self.api_key:
                self.error_occurred.emit("请先设置Gemini API Key")
                return
            
            self.progress_update.emit(f"🤖 正在连接Gemini ({self.model})...")
            
            # 初始化Gemini客户端
            client = genai.Client(api_key=self.api_key)
            
            # 构建专用的系统指令
            system_instruction = self._get_system_instruction()
            
            # 构建完整提示词
            full_prompt = self._build_prompt()
            
            thinking_status = "启用" if self.enable_thinking else "禁用"
            self.progress_update.emit(f"🎨 正在生成HTML动画... (思考模式: {thinking_status})")
            
            # 构建生成配置
            generate_config = types.GenerateContentConfig(
                system_instruction=system_instruction,
                temperature=0.7,
            )
            
            # 根据设置添加思考配置
            if self.enable_thinking:
                generate_config.thinking_config = types.ThinkingConfig(thinking_budget=20000)
            
            # 调用Gemini API
            response = client.models.generate_content(
                model=self.model,
                config=generate_config,
                contents=full_prompt
            )
            
            self.progress_update.emit("✅ 生成完成")
            self.result_ready.emit(response.text)
            
        except Exception as e:
            self.error_occurred.emit(f"生成失败: {str(e)}")
    
    def _get_system_instruction(self) -> str:
        """获取基于动画规范文档的系统指令"""
        base_instruction = """你是一个专业的网页动画开发专家，专门为动画录制工具生成HTML动画代码。

## 核心规范要求

### 必须实现的控制函数
生成的HTML必须包含以下控制函数之一：
- renderAtTime(t) - 主推荐
- updateAtTime(t)
- seekTo(t) 
- goToTime(t)
- setAnimationTime(t)

其中t是时间参数（秒，浮点数），函数必须挂载到window对象：
```javascript
window.renderAtTime = renderAtTime;
```

### 动画控制原理
1. 禁用自动播放 - 动画不能自动播放，必须通过控制函数驱动
2. 时间映射 - 将时间参数t映射到动画的具体状态  
3. 同步渲染 - 调用控制函数后立即渲染对应状态

### 技术要求
- 时间参数t单位为秒，起始值0.0
- 控制函数要快速执行，避免重复DOM查询
- 支持现代浏览器，确保无头浏览器兼容
- 包含边界处理（t=0到duration）

### 严格禁止
- 自动播放动画：setInterval、requestAnimationFrame循环
- 依赖实时时间：Date.now()、performance.now()
- 异步动画：setTimeout、Promise延迟

## 输出要求
- 生成完整可运行的HTML文件
- 包含完整的renderAtTime(t)函数实现
- 确保动画完全由时间参数控制
- 代码要有清晰注释说明控制逻辑
- 不要包含解释文字，只输出HTML代码"""
        
        # 根据动画类型添加特定约束
        if self.animation_type != "混合动画":
            type_constraint = f"\n\n### 动画类型约束\n请严格按照 {self.animation_type} 规范生成代码，不要混合使用其他动画技术。"
            base_instruction += type_constraint
        
        return base_instruction
    
    def _build_prompt(self) -> str:
        """构建完整的提示词"""
        
        # 动画类型特定的技术指导
        type_guidance = {
            "CSS动画": """
生成基于CSS的动画，严格遵循以下要求：
- 只使用CSS变换和样式属性，禁用animation属性
- 在renderAtTime(t)函数中手动计算样式值
- 使用progress = Math.min(t / duration, 1)计算进度
- 通过element.style直接设置样式属性
- 不要使用GSAP、Three.js或其他库
            """,
            "GSAP动画": """
生成基于GSAP的动画，严格遵循以下要求：
- 只使用GSAP库，不要混合CSS动画或Three.js
- 引入GSAP库：<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
- 创建paused时间轴：gsap.timeline({paused: true})
- 在renderAtTime(t)中使用timeline.seek(t)控制进度
- 禁用autoplay和循环
            """,
            "Three.js动画": """
生成基于Three.js的3D动画，严格遵循以下要求：
- 只使用Three.js，不要混合CSS动画或GSAP
- 引入Three.js库：<script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
- 移除requestAnimationFrame循环
- 在renderAtTime(t)中更新3D对象属性并调用renderer.render()
- 根据时间参数控制相机、光照、材质等
            """,
            "JavaScript动画": """
生成基于纯JavaScript的动画，严格遵循以下要求：
- 只使用原生JavaScript，不要使用任何动画库
- 禁用setInterval、setTimeout、requestAnimationFrame
- 在renderAtTime(t)中直接操作DOM元素
- 使用数学函数计算动画状态
- 缓存DOM查询结果避免重复查找
            """,
            "混合动画": """
生成包含多种技术的混合动画，可以组合使用：
- CSS动画 + GSAP缓动
- Three.js 3D场景 + CSS界面动画
- JavaScript粒子系统 + CSS背景动画
- 在单一renderAtTime(t)函数中统一控制所有动画组件
- 确保不同技术间的时间同步
- 禁用所有自动播放机制
            """
        }
        
        guidance = type_guidance.get(self.animation_type, type_guidance["JavaScript动画"])
        
        return f"""
{guidance}

用户动画需求：{self.prompt}

请严格按照动画规范生成完整的HTML文件，确保：

1. 包含完整的renderAtTime(t)函数实现
2. 函数挂载到window对象：window.renderAtTime = renderAtTime
3. 动画完全由时间参数t控制，禁用自动播放
4. 包含适当的边界检查和错误处理
5. 添加必要的注释说明控制逻辑
6. 严格遵循选定的动画类型约束

现在请生成符合规范的HTML代码：
        """

# === 预览控制器 ===
class AnimationPreviewController(QWidget):
    """动画预览控制器"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.html_file = None
        self.duration = 10.0  # 默认动画时长
        self.current_time = 0.0
        self.page_ready = False  # 添加页面就绪状态
        self.setup_ui()
        self.setup_web_engine()  # 新增：配置WebEngine

    def setup_web_engine(self):
        """配置WebEngine支持Three.js等库"""
        from PyQt6.QtWebEngineCore import QWebEngineSettings, QWebEngineProfile

        # 获取默认配置文件
        profile = QWebEngineProfile.defaultProfile()
        settings = profile.settings()

        # 启用WebGL和其他必要功能
        settings.setAttribute(QWebEngineSettings.WebAttribute.WebGLEnabled, True)
        settings.setAttribute(QWebEngineSettings.WebAttribute.Accelerated2dCanvasEnabled, True)
        settings.setAttribute(QWebEngineSettings.WebAttribute.JavascriptEnabled, True)
        settings.setAttribute(QWebEngineSettings.WebAttribute.JavascriptCanOpenWindows, True)
        settings.setAttribute(QWebEngineSettings.WebAttribute.LocalContentCanAccessRemoteUrls, True)
        settings.setAttribute(QWebEngineSettings.WebAttribute.LocalContentCanAccessFileUrls, True)

        # 禁用安全限制以支持本地文件
        settings.setAttribute(QWebEngineSettings.WebAttribute.LocalStorageEnabled, True)
        settings.setAttribute(QWebEngineSettings.WebAttribute.WebGLEnabled, True)

        print("✅ WebEngine配置完成：启用WebGL、Canvas2D、JavaScript")

    def setup_ui(self):
        layout = QVBoxLayout(self)
        
        # 预览窗口
        self.web_view = QWebEngineView()
        self.web_view.setMinimumHeight(400)
        layout.addWidget(self.web_view)

        # 调试信息区域
        debug_group = QGroupBox("调试信息")
        debug_layout = QVBoxLayout(debug_group)
        self.debug_text = QPlainTextEdit()
        self.debug_text.setMaximumHeight(100)
        self.debug_text.setReadOnly(True)
        debug_layout.addWidget(self.debug_text)
        layout.addWidget(debug_group)

        # 控制面板
        control_panel = QGroupBox("预览控制")
        control_layout = QVBoxLayout(control_panel)

        # 页面状态指示
        status_layout = QHBoxLayout()
        self.status_label = QLabel("📄 等待加载...")
        self.status_label.setStyleSheet("color: #666; font-weight: bold;")
        status_layout.addWidget(self.status_label)

        self.reload_btn = QPushButton("🔄 重新加载")
        self.reload_btn.clicked.connect(self.reload_page)
        status_layout.addWidget(self.reload_btn)
        status_layout.addStretch()
        control_layout.addLayout(status_layout)
        
        # 时间滑块
        slider_layout = QHBoxLayout()
        slider_layout.addWidget(QLabel("时间:"))
        
        self.time_slider = QSlider(Qt.Orientation.Horizontal)
        self.time_slider.setRange(0, 1000)
        self.time_slider.setValue(0)
        self.time_slider.valueChanged.connect(self.on_time_changed)
        slider_layout.addWidget(self.time_slider)
        
        self.time_label = QLabel("0.0s / 10.0s")
        slider_layout.addWidget(self.time_label)
        
        control_layout.addLayout(slider_layout)
        
        # 播放控制按钮
        btn_layout = QHBoxLayout()
        
        self.play_btn = QPushButton("▶ 播放")
        self.play_btn.clicked.connect(self.toggle_play)
        btn_layout.addWidget(self.play_btn)
        
        self.reset_btn = QPushButton("⏮ 重置")
        self.reset_btn.clicked.connect(self.reset_animation)
        btn_layout.addWidget(self.reset_btn)

        # 测试按钮
        self.test_btn = QPushButton("🧪 测试渲染")
        self.test_btn.clicked.connect(self.test_render_function)
        btn_layout.addWidget(self.test_btn)

        # 时长设置
        btn_layout.addWidget(QLabel("时长:"))
        self.duration_spinbox = QDoubleSpinBox()
        self.duration_spinbox.setRange(1.0, 60.0)
        self.duration_spinbox.setValue(self.duration)
        self.duration_spinbox.setSuffix("s")
        self.duration_spinbox.valueChanged.connect(self.update_duration)
        btn_layout.addWidget(self.duration_spinbox)
        
        control_layout.addLayout(btn_layout)
        layout.addWidget(control_panel)
        
        # 播放定时器
        self.play_timer = QTimer()
        self.play_timer.timeout.connect(self.advance_time)
        self.play_timer.setInterval(50)  # 20fps预览
        self.is_playing = False

    def debug_log(self, message):
        """添加调试日志"""
        timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
        self.debug_text.appendPlainText(f"[{timestamp}] {message}")
        print(f"[预览调试] {message}")

    def load_html(self, html_file):
        """加载HTML文件"""
        self.html_file = html_file
        self.page_ready = False

        if html_file and os.path.exists(html_file):
            # 断开之前的连接
            try:
                self.web_view.loadFinished.disconnect()
            except:
                pass

            # 连接加载完成信号
            self.web_view.loadFinished.connect(self.on_page_loaded)

            url = QUrl.fromLocalFile(os.path.abspath(html_file))
            self.debug_log(f"开始加载: {url.toString()}")
            self.status_label.setText("📄 正在加载...")
            self.web_view.load(url)
        else:
            self.debug_log(f"文件不存在: {html_file}")
            self.status_label.setText("❌ 文件不存在")

    def reload_page(self):
        """重新加载页面"""
        if self.html_file:
            self.debug_log("手动重新加载页面")
            self.load_html(self.html_file)

    def on_page_loaded(self, success):
        """页面加载完成"""
        if success:
            self.debug_log("页面基础加载完成，等待库加载...")
            self.status_label.setText("⏳ 等待库加载...")

            # 等待外部库加载完成
            self.wait_for_libraries()
        else:
            self.debug_log("❌ 页面加载失败")
            self.status_label.setText("❌ 页面加载失败")

    def wait_for_libraries(self):
        """等待外部库加载完成"""
        check_script = """
        (function() {
            // 检查常见的动画库是否加载完成
            const checks = {
                'THREE.js': typeof THREE !== 'undefined',
                'GSAP': typeof gsap !== 'undefined' || typeof TweenMax !== 'undefined',
                'renderAtTime': typeof window.renderAtTime === 'function'
            };

            const loadedLibs = Object.keys(checks).filter(lib => checks[lib]);
            const missingLibs = Object.keys(checks).filter(lib => !checks[lib]);

            return {
                loaded: loadedLibs,
                missing: missingLibs,
                allReady: missingLibs.length <= 1, // 允许某些库不存在
                hasRenderFunction: checks['renderAtTime']
            };
        })();
        """

        def check_result(result):
            if result:
                loaded = result.get('loaded', [])
                missing = result.get('missing', [])
                all_ready = result.get('allReady', False)
                has_render = result.get('hasRenderFunction', False)

                self.debug_log(f"已加载库: {', '.join(loaded) if loaded else '无'}")
                self.debug_log(f"缺失库: {', '.join(missing) if missing else '无'}")

                if has_render:
                    self.page_ready = True
                    self.status_label.setText("✅ 页面就绪")
                    self.debug_log("✅ renderAtTime函数已就绪")

                    # 初始渲染
                    QTimer.singleShot(100, lambda: self.reset_animation())
                else:
                    self.status_label.setText("⚠️ 无renderAtTime函数")
                    self.debug_log("⚠️ 未找到renderAtTime函数")
            else:
                self.debug_log("❌ 库检查失败")
                self.status_label.setText("❌ 检查失败")

        # 延迟检查，给库一些加载时间
        QTimer.singleShot(1000, lambda: self.web_view.page().runJavaScript(check_script, check_result))

    def test_render_function(self):
        """测试渲染函数"""
        if not self.page_ready:
            self.debug_log("⚠️ 页面还未就绪")
            return

        test_script = """
        (function() {
            try {
                if (typeof window.renderAtTime === 'function') {
                    window.renderAtTime(0.5);
                    return {success: true, message: 'renderAtTime(0.5) 调用成功'};
                } else {
                    return {success: false, message: 'renderAtTime 函数不存在'};
                }
            } catch (error) {
                return {success: false, message: 'Error: ' + error.message};
            }
        })();
        """

        def test_result(result):
            if result:
                success = result.get('success', False)
                message = result.get('message', '未知结果')

                if success:
                    self.debug_log(f"✅ 测试成功: {message}")
                else:
                    self.debug_log(f"❌ 测试失败: {message}")
            else:
                self.debug_log("❌ 测试脚本执行失败")

        self.web_view.page().runJavaScript(test_script, test_result)

    def on_time_changed(self, value):
        """时间滑块变化"""
        if not self.page_ready:
            return

        self.current_time = (value / 1000.0) * self.duration
        self.update_time_display()
        self.render_at_time(self.current_time)
    
    def render_at_time(self, t):
        """渲染指定时间的动画状态"""
        if not self.page_ready or not self.html_file:
            return

        js_code = f"""
        (function() {{
            try {{
                if (typeof window.renderAtTime === 'function') {{
                    window.renderAtTime({t});
                    return {{success: true, time: {t}}};
                }} else {{
                    return {{success: false, error: 'renderAtTime function not found'}};
                }}
            }} catch (error) {{
                return {{success: false, error: error.message}};
            }}
        }})();
        """

        def render_result(result):
            if result and not result.get('success', True):
                error = result.get('error', '未知错误')
                self.debug_log(f"❌ 渲染错误 t={t}: {error}")

        self.web_view.page().runJavaScript(js_code, render_result)
    
    def toggle_play(self):
        """切换播放/暂停"""
        if not self.page_ready:
            self.debug_log("⚠️ 页面未就绪，无法播放")
            return

        if self.is_playing:
            self.pause_animation()
        else:
            self.play_animation()
    
    def play_animation(self):
        """开始播放"""
        if not self.page_ready:
            return

        self.is_playing = True
        self.play_btn.setText("⏸ 暂停")
        self.play_timer.start()
        self.debug_log("▶ 开始播放")

    def pause_animation(self):
        """暂停播放"""
        self.is_playing = False
        self.play_btn.setText("▶ 播放")
        self.play_timer.stop()
        self.debug_log("⏸ 暂停播放")
    
    def reset_animation(self):
        """重置动画"""
        self.pause_animation()
        self.current_time = 0.0
        self.time_slider.setValue(0)
        self.update_time_display()
        self.render_at_time(0.0)
        self.debug_log("⏮ 重置动画")
    
    def advance_time(self):
        """推进时间（播放时调用）"""
        self.current_time += 0.05  # 每次增加50ms
        if self.current_time >= self.duration:
            self.current_time = self.duration
            self.pause_animation()
        
        progress = int((self.current_time / self.duration) * 1000)
        self.time_slider.setValue(progress)
    
    def update_duration(self, duration):
        """更新动画时长"""
        self.duration = duration
        self.update_time_display()
        # 重新计算滑块位置
        if self.duration > 0:
            progress = int((self.current_time / self.duration) * 1000)
            self.time_slider.setValue(progress)
    
    def update_time_display(self):
        """更新时间显示"""
        self.time_label.setText(f"{self.current_time:.1f}s / {self.duration:.1f}s")

# === HTML优化器 ===
class HTMLOptimizer:
    """HTML优化器"""

    def __init__(self):
        self.optimizations = {
            'remove_comments': True,
            'minify_css': True,
            'minify_js': True,
            'remove_empty_lines': True,
            'optimize_images': False,  # 需要额外库
            'validate_render_function': True,
            'add_meta_tags': True
        }

    def optimize_html(self, html_content: str, options: Dict[str, bool] = None) -> str:
        """优化HTML内容"""
        if options:
            self.optimizations.update(options)

        optimized = html_content

        # 移除注释
        if self.optimizations.get('remove_comments', True):
            optimized = self._remove_comments(optimized)

        # 压缩CSS
        if self.optimizations.get('minify_css', True):
            optimized = self._minify_css(optimized)

        # 压缩JavaScript
        if self.optimizations.get('minify_js', True):
            optimized = self._minify_js(optimized)

        # 移除空行
        if self.optimizations.get('remove_empty_lines', True):
            optimized = self._remove_empty_lines(optimized)

        # 验证renderAtTime函数
        if self.optimizations.get('validate_render_function', True):
            optimized = self._ensure_render_function(optimized)

        # 添加meta标签
        if self.optimizations.get('add_meta_tags', True):
            optimized = self._add_meta_tags(optimized)

        return optimized

    def _remove_comments(self, html: str) -> str:
        """移除HTML注释"""
        # 移除HTML注释
        html = re.sub(r'<!--.*?-->', '', html, flags=re.DOTALL)

        # 移除CSS注释
        html = re.sub(r'/\*.*?\*/', '', html, flags=re.DOTALL)

        # 移除JavaScript单行注释（但保留URL中的//）
        lines = html.split('\n')
        cleaned_lines = []
        for line in lines:
            # 检查是否在script标签内
            if '<script' in line or '</script>' in line:
                cleaned_lines.append(line)
            else:
                # 移除//注释，但保留URL
                if '//' in line and 'http' not in line:
                    comment_pos = line.find('//')
                    line = line[:comment_pos].rstrip()
                cleaned_lines.append(line)

        return '\n'.join(cleaned_lines)

    def _minify_css(self, html: str) -> str:
        """压缩CSS"""
        def minify_css_block(match):
            css = match.group(1)
            # 移除多余空白
            css = re.sub(r'\s+', ' ', css)
            # 移除分号前的空格
            css = re.sub(r'\s*;\s*', ';', css)
            # 移除花括号周围的空格
            css = re.sub(r'\s*{\s*', '{', css)
            css = re.sub(r'\s*}\s*', '}', css)
            # 移除冒号周围的空格
            css = re.sub(r'\s*:\s*', ':', css)
            return f'<style>{css.strip()}</style>'

        # 压缩<style>标签中的CSS
        html = re.sub(r'<style[^>]*>(.*?)</style>', minify_css_block, html, flags=re.DOTALL | re.IGNORECASE)

        return html

    def _minify_js(self, html: str) -> str:
        """压缩JavaScript（简单版本）"""
        def minify_js_block(match):
            js = match.group(1)
            # 移除多余空白（保留字符串中的空白）
            lines = js.split('\n')
            minified_lines = []
            for line in lines:
                # 简单的空白压缩
                line = line.strip()
                if line and not line.startswith('//'):
                    # 移除多余空格
                    line = re.sub(r'\s+', ' ', line)
                    minified_lines.append(line)

            return f'<script>{" ".join(minified_lines)}</script>'

        # 压缩<script>标签中的JavaScript
        html = re.sub(r'<script[^>]*>(.*?)</script>', minify_js_block, html, flags=re.DOTALL | re.IGNORECASE)

        return html

    def _remove_empty_lines(self, html: str) -> str:
        """移除空行"""
        lines = html.split('\n')
        non_empty_lines = [line for line in lines if line.strip()]
        return '\n'.join(non_empty_lines)

    def _ensure_render_function(self, html: str) -> str:
        """确保包含renderAtTime函数"""
        if 'window.renderAtTime' in html or 'renderAtTime' in html:
            return html

        # 添加默认的renderAtTime函数
        render_function = '''
<script>
// 默认的renderAtTime函数
function renderAtTime(t) {
    const duration = 10; // 默认10秒动画
    const progress = Math.min(t / duration, 1);

    // 尝试查找可动画的元素
    const animatableElements = document.querySelectorAll('[data-animate], .animate, .animated');
    animatableElements.forEach(el => {
        // 简单的透明度动画
        el.style.opacity = progress;
    });

    console.log('renderAtTime called with t=' + t + ', progress=' + progress);
}

// 挂载到window对象
window.renderAtTime = renderAtTime;
</script>
'''

        # 在</body>前插入
        if '</body>' in html:
            html = html.replace('</body>', render_function + '\n</body>')
        else:
            html += render_function

        return html

    def _add_meta_tags(self, html: str) -> str:
        """添加优化的meta标签"""
        meta_tags = '''
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<meta name="generator" content="Animation Recorder v7.0">
'''

        # 检查是否已有meta标签
        if '<meta' not in html:
            if '<head>' in html:
                html = html.replace('<head>', '<head>' + meta_tags)
            else:
                # 在html标签后添加head
                if '<html' in html:
                    html = html.replace('<html>', '<html>\n<head>' + meta_tags + '\n</head>')

        return html

    def validate_html(self, html_content: str) -> List[str]:
        """验证HTML并返回问题列表"""
        issues = []

        # 检查基本结构
        if not re.search(r'<!DOCTYPE\s+html>', html_content, re.IGNORECASE):
            issues.append("缺少DOCTYPE声明")

        if '<html' not in html_content.lower():
            issues.append("缺少html标签")

        if '<head>' not in html_content.lower():
            issues.append("缺少head标签")

        if '<body>' not in html_content.lower():
            issues.append("缺少body标签")

        # 检查renderAtTime函数
        if 'renderAtTime' not in html_content:
            issues.append("缺少renderAtTime函数")
        elif 'window.renderAtTime' not in html_content:
            issues.append("renderAtTime函数未挂载到window对象")

        # 检查常见错误
        if html_content.count('<script>') != html_content.count('</script>'):
            issues.append("script标签不匹配")

        if html_content.count('<style>') != html_content.count('</style>'):
            issues.append("style标签不匹配")

        return issues

    def get_optimization_report(self, original: str, optimized: str) -> Dict[str, any]:
        """获取优化报告"""
        return {
            'original_size': len(original),
            'optimized_size': len(optimized),
            'compression_ratio': (len(original) - len(optimized)) / len(original) * 100,
            'lines_reduced': original.count('\n') - optimized.count('\n'),
            'has_render_function': 'renderAtTime' in optimized,
            'validation_issues': self.validate_html(optimized)
        }

# === UI美化系统 ===
class ThemeManager:
    """主题管理器"""

    def __init__(self):
        self.themes = {
            'light': {
                'name': '浅色主题',
                'background': '#ffffff',
                'surface': '#f5f5f5',
                'primary': '#2196F3',
                'secondary': '#FFC107',
                'text': '#212121',
                'text_secondary': '#757575',
                'success': '#4CAF50',
                'warning': '#FF9800',
                'error': '#F44336',
                'border': '#E0E0E0'
            },
            'dark': {
                'name': '深色主题',
                'background': '#121212',
                'surface': '#1e1e1e',
                'primary': '#BB86FC',
                'secondary': '#03DAC6',
                'text': '#FFFFFF',
                'text_secondary': '#B0B0B0',
                'success': '#4CAF50',
                'warning': '#FF9800',
                'error': '#CF6679',
                'border': '#333333'
            },
            'blue': {
                'name': '蓝色主题',
                'background': '#f8fbff',
                'surface': '#e3f2fd',
                'primary': '#1976d2',
                'secondary': '#42a5f5',
                'text': '#0d47a1',
                'text_secondary': '#1565c0',
                'success': '#4caf50',
                'warning': '#ff9800',
                'error': '#f44336',
                'border': '#bbdefb'
            }
        }
        self.current_theme = 'light'

    def get_stylesheet(self, theme_name: str = None) -> str:
        """获取主题样式表"""
        if theme_name is None:
            theme_name = self.current_theme

        if theme_name not in self.themes:
            theme_name = 'light'

        theme = self.themes[theme_name]

        return f"""
        /* 全局样式 */
        QMainWindow {{
            background-color: {theme['background']};
            color: {theme['text']};
        }}

        QWidget {{
            background-color: {theme['background']};
            color: {theme['text']};
        }}

        /* 组框样式 */
        QGroupBox {{
            font-weight: bold;
            border: 2px solid {theme['border']};
            border-radius: 8px;
            margin-top: 1ex;
            padding-top: 8px;
            background-color: {theme['surface']};
        }}

        QGroupBox::title {{
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            color: {theme['primary']};
        }}

        /* 按钮样式 */
        QPushButton {{
            background-color: {theme['primary']};
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-weight: bold;
            min-height: 20px;
        }}

        QPushButton:hover {{
            background-color: {theme['secondary']};
        }}

        QPushButton:pressed {{
            background-color: {theme['text_secondary']};
        }}

        QPushButton:disabled {{
            background-color: {theme['border']};
            color: {theme['text_secondary']};
        }}

        /* 特殊按钮样式 */
        QPushButton[objectName="record_btn"] {{
            background-color: {theme['success']};
            font-size: 14px;
            padding: 12px 20px;
        }}

        QPushButton[objectName="stop_btn"] {{
            background-color: {theme['error']};
        }}

        QPushButton[objectName="generate_btn"] {{
            background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                stop:0 {theme['primary']}, stop:1 {theme['secondary']});
            font-size: 13px;
            padding: 12px 20px;
        }}

        /* 输入框样式 */
        QLineEdit, QTextEdit, QPlainTextEdit {{
            border: 2px solid {theme['border']};
            border-radius: 6px;
            padding: 8px;
            background-color: {theme['background']};
            color: {theme['text']};
        }}

        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {{
            border-color: {theme['primary']};
        }}

        /* 下拉框样式 */
        QComboBox {{
            border: 2px solid {theme['border']};
            border-radius: 6px;
            padding: 6px;
            background-color: {theme['background']};
            color: {theme['text']};
            min-width: 6em;
        }}

        QComboBox:focus {{
            border-color: {theme['primary']};
        }}

        QComboBox::drop-down {{
            border: none;
            width: 20px;
        }}

        QComboBox::down-arrow {{
            image: none;
            border-left: 5px solid transparent;
            border-right: 5px solid transparent;
            border-top: 5px solid {theme['text']};
        }}

        /* 进度条样式 */
        QProgressBar {{
            border: 2px solid {theme['border']};
            border-radius: 6px;
            text-align: center;
            background-color: {theme['surface']};
            color: {theme['text']};
        }}

        QProgressBar::chunk {{
            background-color: {theme['primary']};
            border-radius: 4px;
        }}

        /* 滑块样式 */
        QSlider::groove:horizontal {{
            border: 1px solid {theme['border']};
            height: 8px;
            background: {theme['surface']};
            border-radius: 4px;
        }}

        QSlider::handle:horizontal {{
            background: {theme['primary']};
            border: 2px solid {theme['primary']};
            width: 18px;
            margin: -2px 0;
            border-radius: 9px;
        }}

        QSlider::handle:horizontal:hover {{
            background: {theme['secondary']};
            border-color: {theme['secondary']};
        }}

        /* 标签页样式 */
        QTabWidget::pane {{
            border: 2px solid {theme['border']};
            border-radius: 6px;
            background-color: {theme['surface']};
        }}

        QTabBar::tab {{
            background: {theme['background']};
            border: 2px solid {theme['border']};
            padding: 8px 16px;
            margin-right: 2px;
            border-top-left-radius: 6px;
            border-top-right-radius: 6px;
        }}

        QTabBar::tab:selected {{
            background: {theme['primary']};
            color: white;
        }}

        QTabBar::tab:hover {{
            background: {theme['secondary']};
            color: white;
        }}

        /* 列表样式 */
        QListWidget, QTableWidget {{
            border: 2px solid {theme['border']};
            border-radius: 6px;
            background-color: {theme['background']};
            alternate-background-color: {theme['surface']};
        }}

        QListWidget::item, QTableWidget::item {{
            padding: 8px;
            border-bottom: 1px solid {theme['border']};
        }}

        QListWidget::item:selected, QTableWidget::item:selected {{
            background-color: {theme['primary']};
            color: white;
        }}

        QListWidget::item:hover, QTableWidget::item:hover {{
            background-color: {theme['secondary']};
            color: white;
        }}

        /* 菜单样式 */
        QMenuBar {{
            background-color: {theme['surface']};
            border-bottom: 1px solid {theme['border']};
        }}

        QMenuBar::item {{
            spacing: 3px;
            padding: 8px 12px;
            background: transparent;
            border-radius: 4px;
        }}

        QMenuBar::item:selected {{
            background: {theme['primary']};
            color: white;
        }}

        QMenu {{
            background-color: {theme['surface']};
            border: 2px solid {theme['border']};
            border-radius: 6px;
        }}

        QMenu::item {{
            padding: 8px 24px;
        }}

        QMenu::item:selected {{
            background-color: {theme['primary']};
            color: white;
        }}

        /* 状态栏样式 */
        QStatusBar {{
            background-color: {theme['surface']};
            border-top: 1px solid {theme['border']};
            color: {theme['text_secondary']};
        }}

        /* 滚动条样式 */
        QScrollBar:vertical {{
            background: {theme['surface']};
            width: 12px;
            border-radius: 6px;
        }}

        QScrollBar::handle:vertical {{
            background: {theme['primary']};
            border-radius: 6px;
            min-height: 20px;
        }}

        QScrollBar::handle:vertical:hover {{
            background: {theme['secondary']};
        }}

        /* 工具提示样式 */
        QToolTip {{
            background-color: {theme['text']};
            color: {theme['background']};
            border: 1px solid {theme['border']};
            border-radius: 4px;
            padding: 4px;
        }}
        """

    def apply_theme(self, app, theme_name: str):
        """应用主题"""
        self.current_theme = theme_name
        stylesheet = self.get_stylesheet(theme_name)

        # 获取DPI缩放因子来调整样式
        screen = app.primaryScreen()
        logical_dpi = screen.logicalDotsPerInch()
        scale_factor = logical_dpi / 96.0

        # 调整样式表中的尺寸
        stylesheet = self._scale_stylesheet(stylesheet, scale_factor)

        app.setStyleSheet(stylesheet)

    def _scale_stylesheet(self, stylesheet: str, scale_factor: float) -> str:
        """根据DPI缩放调整样式表"""
        import re

        # 如果缩放因子接近1，不需要调整
        if abs(scale_factor - 1.0) < 0.1:
            return stylesheet

        # 调整padding值
        def scale_padding(match):
            value = int(match.group(1))
            scaled_value = max(1, int(value / scale_factor))
            return f"padding: {scaled_value}px"

        # 调整margin值
        def scale_margin(match):
            value = int(match.group(1))
            scaled_value = max(1, int(value / scale_factor))
            return f"margin: {scaled_value}px"

        # 调整border-radius值
        def scale_border_radius(match):
            value = int(match.group(1))
            scaled_value = max(1, int(value / scale_factor))
            return f"border-radius: {scaled_value}px"

        # 调整min-height值
        def scale_min_height(match):
            value = int(match.group(1))
            scaled_value = max(10, int(value / scale_factor))
            return f"min-height: {scaled_value}px"

        # 应用缩放
        stylesheet = re.sub(r'padding:\s*(\d+)px', scale_padding, stylesheet)
        stylesheet = re.sub(r'margin:\s*(\d+)px', scale_margin, stylesheet)
        stylesheet = re.sub(r'border-radius:\s*(\d+)px', scale_border_radius, stylesheet)
        stylesheet = re.sub(r'min-height:\s*(\d+)px', scale_min_height, stylesheet)

        return stylesheet

# === 图标管理器 ===
class IconManager:
    """图标管理器"""

    def __init__(self):
        # Unicode图标映射
        self.icons = {
            'play': '▶️',
            'pause': '⏸️',
            'stop': '⏹️',
            'record': '🔴',
            'preview': '👁️',
            'settings': '⚙️',
            'folder': '📁',
            'file': '📄',
            'save': '💾',
            'open': '📂',
            'new': '➕',
            'delete': '🗑️',
            'edit': '✏️',
            'search': '🔍',
            'download': '⬇️',
            'upload': '⬆️',
            'warning': '⚠️',
            'error': '❌',
            'success': '✅',
            'info': 'ℹ️',
            'help': '❓',
            'home': '🏠',
            'back': '◀️',
            'forward': '▶️',
            'refresh': '🔄',
            'close': '❌',
            'minimize': '➖',
            'maximize': '⬜',
            'library': '📚',
            'project': '📋',
            'animation': '🎬',
            'ai': '🤖',
            'tools': '🔧',
            'performance': '📊',
            'theme': '🎨'
        }

    def get_icon(self, name: str) -> str:
        """获取图标"""
        return self.icons.get(name, '❓')

    def create_icon_button(self, icon_name: str, text: str, tooltip: str = None) -> QPushButton:
        """创建图标按钮"""
        icon = self.get_icon(icon_name)
        button = QPushButton(f"{icon} {text}")
        if tooltip:
            button.setToolTip(tooltip)
        return button

# === 动画小部件 ===
class AnimatedWidget(QWidget):
    """带动画效果的小部件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        try:
            from PyQt6.QtWidgets import QGraphicsOpacityEffect
            from PyQt6.QtCore import QPropertyAnimation

            self.opacity_effect = QGraphicsOpacityEffect()
            self.setGraphicsEffect(self.opacity_effect)
            self.animation = QPropertyAnimation(self.opacity_effect, b"opacity")
        except ImportError:
            # 如果没有动画支持，创建空的方法
            self.opacity_effect = None
            self.animation = None

    def fade_in(self, duration=300):
        """淡入动画"""
        if self.animation:
            self.animation.setDuration(duration)
            self.animation.setStartValue(0.0)
            self.animation.setEndValue(1.0)
            self.animation.start()

    def fade_out(self, duration=300):
        """淡出动画"""
        if self.animation:
            self.animation.setDuration(duration)
            self.animation.setStartValue(1.0)
            self.animation.setEndValue(0.0)
            self.animation.start()

# === 实用工具集 ===
class UtilityToolsManager:
    """实用工具集管理器"""

    def __init__(self, main_window=None):
        self.main_window = main_window
        self.tools = {
            'resolution_calculator': self.resolution_calculator,
            'color_picker': self.color_picker,
            'format_converter': self.format_converter,
            'compression_presets': self.compression_presets,
            'frame_calculator': self.frame_calculator,
            'file_size_calculator': self.file_size_calculator
        }

    def resolution_calculator(self) -> QDialog:
        """分辨率计算器"""
        dialog = QDialog()
        dialog.setWindowTitle("📐 分辨率计算器")
        dialog.setMinimumSize(400, 300)
        layout = QVBoxLayout(dialog)

        # 常用预设
        presets_group = QGroupBox("常用分辨率预设")
        presets_layout = QVBoxLayout(presets_group)

        presets = [
            ("720p (HD)", 1280, 720),
            ("1080p (Full HD)", 1920, 1080),
            ("1440p (2K)", 2560, 1440),
            ("4K (UHD)", 3840, 2160),
            ("Instagram (方形)", 1080, 1080),
            ("Instagram (竖屏)", 1080, 1920),
            ("YouTube (16:9)", 1920, 1080),
            ("Twitter (16:9)", 1200, 675)
        ]

        for name, width, height in presets:
            btn = QPushButton(f"{name} - {width}x{height}")
            btn.clicked.connect(lambda checked=False, w=width, h=height: self.apply_resolution(w, h))
            presets_layout.addWidget(btn)

        layout.addWidget(presets_group)

        # 自定义计算
        custom_group = QGroupBox("自定义计算")
        custom_layout = QGridLayout(custom_group)

        custom_layout.addWidget(QLabel("宽度:"), 0, 0)
        width_input = QSpinBox()
        width_input.setRange(100, 7680)
        width_input.setValue(1920)
        custom_layout.addWidget(width_input, 0, 1)

        custom_layout.addWidget(QLabel("高度:"), 1, 0)
        height_input = QSpinBox()
        height_input.setRange(100, 4320)
        height_input.setValue(1080)
        custom_layout.addWidget(height_input, 1, 1)

        # 长宽比显示
        ratio_label = QLabel("长宽比: 16:9")
        custom_layout.addWidget(ratio_label, 2, 0, 1, 2)

        def update_ratio():
            w, h = width_input.value(), height_input.value()
            from math import gcd
            g = gcd(w, h)
            ratio_label.setText(f"长宽比: {w//g}:{h//g}")

        width_input.valueChanged.connect(update_ratio)
        height_input.valueChanged.connect(update_ratio)

        layout.addWidget(custom_group)

        # 按钮
        buttons_layout = QHBoxLayout()
        apply_btn = QPushButton("应用到配置")
        apply_btn.clicked.connect(lambda: self.apply_resolution(width_input.value(), height_input.value()))
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(dialog.close)

        buttons_layout.addWidget(apply_btn)
        buttons_layout.addWidget(close_btn)
        layout.addLayout(buttons_layout)

        return dialog

    def color_picker(self) -> QDialog:
        """颜色选择器"""
        dialog = QDialog()
        dialog.setWindowTitle("🎨 颜色选择器")
        dialog.setMinimumSize(500, 400)
        layout = QVBoxLayout(dialog)

        # 颜色选择器
        try:
            from PyQt6.QtWidgets import QColorDialog
            color_dialog = QColorDialog()
            color_dialog.setOption(QColorDialog.ColorDialogOption.ShowAlphaChannel, True)
            layout.addWidget(color_dialog)
        except ImportError:
            # 简化版本
            layout.addWidget(QLabel("颜色选择器功能需要完整的PyQt6支持"))

        # 颜色代码显示
        codes_group = QGroupBox("颜色代码")
        codes_layout = QGridLayout(codes_group)

        hex_label = QLabel("HEX:")
        hex_input = QLineEdit("#FF6B6B")
        hex_input.setReadOnly(True)

        rgb_label = QLabel("RGB:")
        rgb_input = QLineEdit("rgb(255, 107, 107)")
        rgb_input.setReadOnly(True)

        hsl_label = QLabel("HSL:")
        hsl_input = QLineEdit("hsl(0, 100%, 71%)")
        hsl_input.setReadOnly(True)

        codes_layout.addWidget(hex_label, 0, 0)
        codes_layout.addWidget(hex_input, 0, 1)
        codes_layout.addWidget(rgb_label, 1, 0)
        codes_layout.addWidget(rgb_input, 1, 1)
        codes_layout.addWidget(hsl_label, 2, 0)
        codes_layout.addWidget(hsl_input, 2, 1)

        layout.addWidget(codes_group)

        # 复制按钮
        copy_layout = QHBoxLayout()
        copy_hex_btn = QPushButton("复制 HEX")
        copy_rgb_btn = QPushButton("复制 RGB")
        copy_hsl_btn = QPushButton("复制 HSL")

        copy_hex_btn.clicked.connect(lambda: QApplication.clipboard().setText(hex_input.text()))
        copy_rgb_btn.clicked.connect(lambda: QApplication.clipboard().setText(rgb_input.text()))
        copy_hsl_btn.clicked.connect(lambda: QApplication.clipboard().setText(hsl_input.text()))

        copy_layout.addWidget(copy_hex_btn)
        copy_layout.addWidget(copy_rgb_btn)
        copy_layout.addWidget(copy_hsl_btn)
        layout.addLayout(copy_layout)

        return dialog

    def format_converter(self) -> QDialog:
        """格式转换器"""
        dialog = QDialog()
        dialog.setWindowTitle("🔄 格式转换器")
        dialog.setMinimumSize(400, 300)
        layout = QVBoxLayout(dialog)

        # 输出格式选择
        format_group = QGroupBox("输出格式")
        format_layout = QVBoxLayout(format_group)

        self.format_mp4 = QRadioButton("MP4 (推荐)")
        self.format_mp4.setChecked(True)
        self.format_webm = QRadioButton("WebM")
        self.format_gif = QRadioButton("GIF")
        self.format_mov = QRadioButton("MOV")

        format_layout.addWidget(self.format_mp4)
        format_layout.addWidget(self.format_webm)
        format_layout.addWidget(self.format_gif)
        format_layout.addWidget(self.format_mov)

        layout.addWidget(format_group)

        # 质量预设
        quality_group = QGroupBox("质量预设")
        quality_layout = QVBoxLayout(quality_group)

        quality_presets = [
            ("高质量 (CRF 18)", 18),
            ("标准质量 (CRF 23)", 23),
            ("快速编码 (CRF 28)", 28),
            ("极小文件 (CRF 35)", 35)
        ]

        try:
            from PyQt6.QtWidgets import QButtonGroup
            self.quality_buttons = QButtonGroup()
            for i, (name, crf) in enumerate(quality_presets):
                radio = QRadioButton(name)
                if i == 1:  # 默认选择标准质量
                    radio.setChecked(True)
                self.quality_buttons.addButton(radio, crf)
                quality_layout.addWidget(radio)
        except ImportError:
            # 简化版本
            for name, crf in quality_presets:
                quality_layout.addWidget(QLabel(name))

        layout.addWidget(quality_group)

        # 预计文件大小
        size_label = QLabel("预计文件大小: 计算中...")
        layout.addWidget(size_label)

        # 应用按钮
        apply_btn = QPushButton("应用设置")
        apply_btn.clicked.connect(lambda: self.apply_format_settings())
        layout.addWidget(apply_btn)

        return dialog

    def compression_presets(self) -> Dict[str, Dict]:
        """压缩预设"""
        return {
            'high_quality': {
                'name': '高质量',
                'crf': 18,
                'preset': 'slow',
                'description': '最佳质量，文件较大'
            },
            'standard': {
                'name': '标准',
                'crf': 23,
                'preset': 'medium',
                'description': '平衡质量和大小'
            },
            'fast': {
                'name': '快速',
                'crf': 28,
                'preset': 'fast',
                'description': '快速编码，质量较低'
            },
            'tiny': {
                'name': '极小',
                'crf': 35,
                'preset': 'veryfast',
                'description': '最小文件，质量最低'
            }
        }

    def frame_calculator(self) -> QDialog:
        """帧数计算器"""
        dialog = QDialog()
        dialog.setWindowTitle("🎞️ 帧数计算器")
        dialog.setMinimumSize(350, 250)
        layout = QVBoxLayout(dialog)

        # 输入区域
        input_group = QGroupBox("参数输入")
        input_layout = QGridLayout(input_group)

        input_layout.addWidget(QLabel("动画时长 (秒):"), 0, 0)
        duration_input = QDoubleSpinBox()
        duration_input.setRange(0.1, 300.0)
        duration_input.setValue(10.0)
        duration_input.setSuffix(" 秒")
        input_layout.addWidget(duration_input, 0, 1)

        input_layout.addWidget(QLabel("帧率 (FPS):"), 1, 0)
        fps_input = QSpinBox()
        fps_input.setRange(1, 120)
        fps_input.setValue(60)
        fps_input.setSuffix(" FPS")
        input_layout.addWidget(fps_input, 1, 1)

        layout.addWidget(input_group)

        # 结果显示
        result_group = QGroupBox("计算结果")
        result_layout = QVBoxLayout(result_group)

        total_frames_label = QLabel("总帧数: 600")
        file_size_label = QLabel("预计文件大小: ~50MB")
        render_time_label = QLabel("预计录制时间: ~10分钟")

        result_layout.addWidget(total_frames_label)
        result_layout.addWidget(file_size_label)
        result_layout.addWidget(render_time_label)

        layout.addWidget(result_group)

        def update_calculations():
            duration = duration_input.value()
            fps = fps_input.value()
            total_frames = int(duration * fps)

            # 估算文件大小 (假设每帧约50KB)
            estimated_size_mb = (total_frames * 50) / 1024

            # 估算录制时间 (假设每帧需要0.5秒处理)
            estimated_time_minutes = (total_frames * 0.5) / 60

            total_frames_label.setText(f"总帧数: {total_frames}")
            file_size_label.setText(f"预计文件大小: ~{estimated_size_mb:.1f}MB")
            render_time_label.setText(f"预计录制时间: ~{estimated_time_minutes:.1f}分钟")

        duration_input.valueChanged.connect(update_calculations)
        fps_input.valueChanged.connect(update_calculations)

        # 应用按钮
        apply_btn = QPushButton("应用到配置")
        apply_btn.clicked.connect(lambda: self.apply_frame_settings(
            int(duration_input.value() * fps_input.value()),
            fps_input.value()
        ))
        layout.addWidget(apply_btn)

        return dialog

    def file_size_calculator(self) -> QDialog:
        """文件大小计算器"""
        dialog = QDialog()
        dialog.setWindowTitle("📊 文件大小计算器")
        dialog.setMinimumSize(300, 200)
        layout = QVBoxLayout(dialog)

        info_label = QLabel("根据分辨率、帧率和时长估算文件大小")
        layout.addWidget(info_label)

        # 简单的估算显示
        estimate_label = QLabel("当前配置预计文件大小: ~25MB")
        layout.addWidget(estimate_label)

        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(dialog.close)
        layout.addWidget(close_btn)

        return dialog

    # 这些方法调用MainWindow的方法
    def set_resolution(self, width: int, height: int):
        if self.main_window:
            self.main_window.set_resolution_from_tool(width, height)

    def apply_resolution(self, width: int, height: int):
        if self.main_window:
            self.main_window.apply_resolution_from_tool(width, height)

    def apply_format_settings(self):
        if self.main_window:
            self.main_window.log("🔄 格式设置已应用")

    def apply_frame_settings(self, total_frames: int, fps: int):
        if self.main_window:
            self.main_window.log(f"🎞️ 帧设置已应用: {total_frames}帧, {fps}FPS")

# === 性能监控系统 ===
@dataclass
class PerformanceMetrics:
    timestamp: float
    cpu_percent: float
    memory_percent: float
    memory_mb: float
    disk_io_read: int = 0
    disk_io_write: int = 0
    network_sent: int = 0
    network_recv: int = 0

class PerformanceMonitor(QThread):
    """性能监控器"""

    metrics_updated = pyqtSignal(PerformanceMetrics)
    warning_issued = pyqtSignal(str, str)  # 类型, 消息

    def __init__(self):
        super().__init__()
        self.monitoring = False
        self.warning_thresholds = {
            'cpu': 80.0,     # CPU使用率阈值
            'memory': 85.0,  # 内存使用率阈值
            'memory_mb': 2048  # 内存MB阈值
        }
        self.metrics_history = []
        self.max_history = 300  # 保存5分钟历史(每秒一次)

    def start_monitoring(self):
        """开始监控"""
        self.monitoring = True
        self.start()

    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        self.quit()
        self.wait()

    def run(self):
        """监控主循环"""
        while self.monitoring:
            try:
                metrics = self._collect_metrics()

                # 检查警告条件
                self._check_warnings(metrics)

                # 保存历史记录
                self.metrics_history.append(metrics)
                if len(self.metrics_history) > self.max_history:
                    self.metrics_history.pop(0)

                # 发送更新信号
                self.metrics_updated.emit(metrics)

                time.sleep(1)  # 每秒更新一次

            except Exception as e:
                print(f"性能监控错误: {e}")
                time.sleep(1)

    def _collect_metrics(self) -> PerformanceMetrics:
        """收集性能指标"""
        if psutil is None:
            # 如果psutil不可用，返回模拟数据
            return PerformanceMetrics(
                timestamp=time.time(),
                cpu_percent=0.0,
                memory_percent=0.0,
                memory_mb=0.0
            )

        # 获取系统性能数据
        cpu_percent = psutil.cpu_percent(interval=0.1)
        memory = psutil.virtual_memory()

        # 网络和磁盘IO（可选）
        disk_io = psutil.disk_io_counters()
        network_io = psutil.net_io_counters()

        return PerformanceMetrics(
            timestamp=time.time(),
            cpu_percent=cpu_percent,
            memory_percent=memory.percent,
            memory_mb=memory.used / 1024 / 1024,
            disk_io_read=disk_io.read_bytes if disk_io else 0,
            disk_io_write=disk_io.write_bytes if disk_io else 0,
            network_sent=network_io.bytes_sent if network_io else 0,
            network_recv=network_io.bytes_recv if network_io else 0
        )

    def _check_warnings(self, metrics: PerformanceMetrics):
        """检查警告条件"""
        if metrics.cpu_percent > self.warning_thresholds['cpu']:
            self.warning_issued.emit(
                "CPU",
                f"CPU使用率过高: {metrics.cpu_percent:.1f}%"
            )

        if metrics.memory_percent > self.warning_thresholds['memory']:
            self.warning_issued.emit(
                "内存",
                f"内存使用率过高: {metrics.memory_percent:.1f}%"
            )

        if metrics.memory_mb > self.warning_thresholds['memory_mb']:
            self.warning_issued.emit(
                "内存",
                f"内存使用量过高: {metrics.memory_mb:.0f}MB"
            )

    def get_average_metrics(self, seconds: int = 60) -> PerformanceMetrics:
        """获取平均性能指标"""
        if not self.metrics_history:
            return PerformanceMetrics(0, 0, 0, 0)

        recent_metrics = self.metrics_history[-seconds:]
        if not recent_metrics:
            recent_metrics = self.metrics_history

        avg_cpu = sum(m.cpu_percent for m in recent_metrics) / len(recent_metrics)
        avg_memory_percent = sum(m.memory_percent for m in recent_metrics) / len(recent_metrics)
        avg_memory_mb = sum(m.memory_mb for m in recent_metrics) / len(recent_metrics)

        return PerformanceMetrics(
            timestamp=time.time(),
            cpu_percent=avg_cpu,
            memory_percent=avg_memory_percent,
            memory_mb=avg_memory_mb
        )

class RecordingPerformanceMonitor:
    """录制性能监控器"""

    def __init__(self):
        self.recording_start_time = 0
        self.recording_metrics = []
        self.frame_times = []

    def start_recording_monitor(self):
        """开始录制监控"""
        self.recording_start_time = time.time()
        self.recording_metrics = []
        self.frame_times = []

    def log_frame_time(self, frame_number: int):
        """记录帧时间"""
        current_time = time.time()
        if self.recording_start_time > 0:
            elapsed = current_time - self.recording_start_time
            self.frame_times.append((frame_number, elapsed))

    def get_recording_stats(self) -> Dict:
        """获取录制统计信息"""
        if not self.frame_times:
            return {}

        total_time = self.frame_times[-1][1] if self.frame_times else 0
        total_frames = len(self.frame_times)
        avg_fps = total_frames / total_time if total_time > 0 else 0

        return {
            'total_time': total_time,
            'total_frames': total_frames,
            'average_fps': avg_fps,
            'expected_fps': 0,  # 从配置获取
            'performance_ratio': 0  # avg_fps / expected_fps
        }

# === 统一管理器 ===
class AppManager:
    """统一管理所有核心功能"""
    
    def __init__(self):
        self.config = Config.load()
        self.detection = None
        self.recorder = None
        self.gemini_generator = None
        self.theme_manager = ThemeManager()  # 新增主题管理器
        self.icon_manager = IconManager()  # 新增图标管理器
        self.utility_tools = None  # 工具管理器将在MainWindow中初始化
        
        # 动画检测模式
        self.detection_patterns = {
            AnimationType.CSS_ANIMATION: [r'@keyframes', r'animation\s*:', r'transform\s*:', r'transition\s*:'],
            AnimationType.GSAP: [r'gsap\.', r'TweenMax', r'TimelineMax', r'\.to\(', r'\.from\('],
            AnimationType.THREE_JS: [r'THREE\.', r'WebGLRenderer', r'PerspectiveCamera', r'Scene\(\)'],
            AnimationType.JAVASCRIPT: [r'requestAnimationFrame', r'setInterval', r'element\.style', r'canvas\.getContext']
        }
        
        # 控制代码模板
        self.templates = {
            AnimationType.CSS_ANIMATION: self._css_template,
            AnimationType.GSAP: self._gsap_template,
            AnimationType.THREE_JS: self._threejs_template,
            AnimationType.JAVASCRIPT: self._js_template
        }
    
    def detect_animation(self, html_content: str) -> DetectionResult:
        """智能检测动画类型"""
        scores = {}
        for anim_type, patterns in self.detection_patterns.items():
            score = sum(1 for pattern in patterns 
                       if re.search(pattern, html_content, re.I))
            if score > 0:
                scores[anim_type] = score / len(patterns)
        
        if not scores:
            return DetectionResult(AnimationType.UNKNOWN, 0.0, ["未检测到动画"])
        
        # 如果检测到多种类型，可能是混合动画
        if len(scores) > 1:
            best_type = AnimationType.MIXED
            confidence = sum(scores.values()) / len(scores)
            suggestions = [f"检测到混合动画: {', '.join(t.value for t in scores.keys())}"]
        else:
            best_type = max(scores, key=scores.get)
            confidence = scores[best_type]
            suggestions = [f"检测到 {best_type.value} 动画"]
        
        control_code = self.templates.get(best_type, lambda: "")()
        
        return DetectionResult(best_type, confidence, suggestions, control_code)
    
    def generate_html_with_gemini(self, prompt: str, animation_type: str) -> GeminiGenerator:
        """使用Gemini生成HTML动画"""
        self.gemini_generator = GeminiGenerator(
            self.config.gemini_api_key, 
            prompt, 
            animation_type,
            self.config.gemini_model,
            self.config.enable_thinking
        )
        return self.gemini_generator
    
    def _css_template(self) -> str:
        return """
function renderAtTime(t) {
    const duration = {duration};
    const progress = Math.min(t / duration, 1);
    
    document.querySelectorAll('[data-animate]').forEach(el => {
        const startVal = parseFloat(el.dataset.start || 0);
        const endVal = parseFloat(el.dataset.end || 100);
        const currentVal = startVal + (endVal - startVal) * progress;
        el.style.transform = `translateX(${{currentVal}}px)`;
    });
}
window.renderAtTime = renderAtTime;
        """.format(duration=self.config.total_frames / self.config.fps)
    
    def _gsap_template(self) -> str:
        return """
let timeline = gsap.timeline({paused: true});
// 在这里添加GSAP动画
function renderAtTime(t) {
    if (timeline) {
        timeline.seek(t);
    }
}
window.renderAtTime = renderAtTime;
        """
    
    def _threejs_template(self) -> str:
        return """
function renderAtTime(t) {
    // 更新Three.js场景
    if (window.scene && window.renderer && window.camera) {
        // 在这里根据时间t更新3D对象
        window.renderer.render(window.scene, window.camera);
    }
}
window.renderAtTime = renderAtTime;
        """
    
    def _js_template(self) -> str:
        return """
function renderAtTime(t) {
    const duration = {duration};
    const progress = Math.min(t / duration, 1);
    // 在这里添加JavaScript动画逻辑
    console.log('Time:', t, 'Progress:', progress);
}
window.renderAtTime = renderAtTime;
        """.format(duration=self.config.total_frames / self.config.fps)

# === 交互事件记录器 ===
class InteractionRecorder:
    def __init__(self):
        self.events = []
        self.recording = False
        self.start_time = 0

    def start_recording(self):
        self.events = []
        self.recording = True
        self.start_time = time.time()

    def add_event(self, event: InteractionEvent):
        if self.recording:
            event.timestamp = time.time() - self.start_time
            self.events.append(event)

    def stop_recording(self):
        self.recording = False
        return self.events

    def save_to_file(self, filepath: str):
        with open(filepath, 'w') as f:
            json.dump([asdict(event) for event in self.events], f, indent=2)

# === 交互模拟器 ===
class InteractionSimulator:
    def __init__(self, page):
        self.page = page

    async def simulate_event(self, event: InteractionEvent):
        try:
            if event.event_type == 'click':
                await self.page.click(event.target_element)
            elif event.event_type == 'scroll':
                await self.page.evaluate(f'''
                    window.scrollTo({event.scroll_position['scrollX']},
                                   {event.scroll_position['scrollY']})
                ''')
            elif event.event_type == 'hover':
                await self.page.hover(event.target_element)
            # 等待动画完成
            await self.page.wait_for_timeout(100)
        except Exception as e:
            print(f"模拟事件失败: {e}")

# === 录制器 ===
class Recorder(QThread):
    progress = pyqtSignal(int, int)
    log = pyqtSignal(str)
    finished = pyqtSignal(str)
    
    def __init__(self, config: Config):
        super().__init__()
        self.config = config
        self.running = True
    
    def run(self):
        try:
            if not PLAYWRIGHT_AVAILABLE:
                self.log.emit("❌ Playwright未安装，请运行: pip install playwright && playwright install")
                self.finished.emit("失败: Playwright未安装")
                return
                
            self.log.emit("🎬 开始录制...")

            with sync_playwright() as p:
                browser = p.chromium.launch(headless=True)
                page = browser.new_page(viewport={'width': self.config.width, 'height': self.config.height})

                # 加载页面
                page.goto(f"file:///{os.path.abspath(self.config.html_file)}")
                page.wait_for_timeout(2000)

                # 根据动画类型选择录制策略
                if self.config.animation_type.startswith('interactive'):
                    self._record_interactive_animation(page)
                else:
                    self._record_time_based_animation(page)

                browser.close()
                self.finished.emit(self.config.output_path)
                
        except Exception as e:
            self.log.emit(f"❌ 录制失败: {e}")
            self.finished.emit(f"失败: {e}")
    
    def _record_time_based_animation(self, page):
        """录制时间驱动动画"""
        # FFmpeg进程
        ffmpeg_cmd = self._build_ffmpeg_cmd()
        ffmpeg = subprocess.Popen(ffmpeg_cmd, stdin=subprocess.PIPE)

        # 逐帧录制
        for frame in range(self.config.total_frames):
            if not self.running:
                break

            current_time = frame / self.config.fps

            # 调用控制函数
            page.evaluate(f"if(window.renderAtTime) window.renderAtTime({current_time})")
            page.wait_for_timeout(50)

            # 截图
            screenshot = page.screenshot(type='png')
            ffmpeg.stdin.write(screenshot)

            self.progress.emit(frame + 1, self.config.total_frames)

        ffmpeg.stdin.close()
        ffmpeg.wait()

    def _record_interactive_animation(self, page):
        """录制交互式动画"""
        # 加载事件序列
        if hasattr(self.config, 'interaction_file') and os.path.exists(self.config.interaction_file):
            with open(self.config.interaction_file, 'r') as f:
                events = [InteractionEvent(**data) for data in json.load(f)]
        else:
            # 使用默认交互序列
            events = self._generate_default_interactions()

        simulator = InteractionSimulator(page)
        ffmpeg_cmd = self._build_ffmpeg_cmd()
        ffmpeg = subprocess.Popen(ffmpeg_cmd, stdin=subprocess.PIPE)

        current_time = 0
        frame_count = 0

        for event in events:
            # 等待到事件时间
            while current_time < event.timestamp:
                if not self.running: break
                screenshot = page.screenshot(type='png')
                ffmpeg.stdin.write(screenshot)
                frame_count += 1
                current_time += 1.0 / self.config.fps
                self.progress.emit(frame_count, self.config.total_frames)

            # 执行交互事件
            import asyncio
            asyncio.run(simulator.simulate_event(event))

            # 录制事件后的动画帧
            animation_duration = 2.0  # 每个事件后录制2秒动画
            animation_frames = int(animation_duration * self.config.fps)

            for _ in range(animation_frames):
                if not self.running: break
                screenshot = page.screenshot(type='png')
                ffmpeg.stdin.write(screenshot)
                frame_count += 1
                self.progress.emit(frame_count, self.config.total_frames)

        ffmpeg.stdin.close()
        ffmpeg.wait()

    def _generate_default_interactions(self):
        """生成默认交互序列"""
        return [
            InteractionEvent(0.0, 'scroll', 'body', {}, {'scrollX': 0, 'scrollY': 0}),
            InteractionEvent(2.0, 'scroll', 'body', {}, {'scrollX': 0, 'scrollY': 500}),
            InteractionEvent(4.0, 'click', '.interactive-element', {'x': 500, 'y': 300}, {}),
            InteractionEvent(6.0, 'scroll', 'body', {}, {'scrollX': 0, 'scrollY': 1000}),
        ]

    def _build_ffmpeg_cmd(self):
        return [
            'ffmpeg', '-f', 'image2pipe', '-vcodec', 'png',
            '-r', str(self.config.fps), '-i', '-',
            '-c:v', 'libx264', '-crf', str(self.config.crf),
            '-pix_fmt', 'yuv420p', '-y', self.config.output_path
        ]

    def stop(self):
        self.running = False

# === 交互录制窗口 ===
class InteractionRecordingWindow(QDialog):
    interactions_recorded = pyqtSignal(str)

    def __init__(self, html_file, parent=None):
        super().__init__(parent)
        self.html_file = html_file
        self.recorder = InteractionRecorder()
        self.setup_ui()

    def setup_ui(self):
        self.setWindowTitle("交互序列录制")
        self.setMinimumSize(800, 600)
        layout = QVBoxLayout(self)

        # 说明文本
        info_label = QLabel("""
        <h3>交互序列录制</h3>
        <p>在下方的网页中进行交互操作，系统会自动记录您的操作序列。</p>
        <p>支持的操作：点击、滚动、悬停等</p>
        """)
        layout.addWidget(info_label)

        # 网页视图
        self.web_view = QWebEngineView()
        self.web_view.setMinimumHeight(400)
        layout.addWidget(self.web_view)

        # 控制按钮
        btn_layout = QHBoxLayout()

        self.start_btn = QPushButton("🔴 开始录制")
        self.start_btn.clicked.connect(self.start_recording)
        btn_layout.addWidget(self.start_btn)

        self.stop_btn = QPushButton("⏹ 停止录制")
        self.stop_btn.clicked.connect(self.stop_recording)
        self.stop_btn.setEnabled(False)
        btn_layout.addWidget(self.stop_btn)

        self.save_btn = QPushButton("💾 保存序列")
        self.save_btn.clicked.connect(self.save_sequence)
        self.save_btn.setEnabled(False)
        btn_layout.addWidget(self.save_btn)

        layout.addLayout(btn_layout)

        # 加载HTML文件
        if self.html_file and os.path.exists(self.html_file):
            url = QUrl.fromLocalFile(os.path.abspath(self.html_file))
            self.web_view.load(url)

    def start_recording(self):
        """开始录制交互"""
        self.recorder.start_recording()
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.start_btn.setText("🔴 录制中...")

        # 这里可以添加JavaScript来监听页面事件
        # 简化版本：使用定时器模拟事件记录

    def stop_recording(self):
        """停止录制交互"""
        self.events = self.recorder.stop_recording()
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.save_btn.setEnabled(True)
        self.start_btn.setText("🔴 开始录制")

    def save_sequence(self):
        """保存交互序列"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存交互序列", "interactions.json", "JSON文件 (*.json)"
        )
        if file_path:
            self.recorder.save_to_file(file_path)
            self.interactions_recorded.emit(file_path)
            self.accept()

# === 主界面 ===
class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("🎬 网页动画录制工具 v7.0 - 专业版")

        # 根据DPI缩放设置合适的窗口尺寸
        # 使用更小的默认尺寸，避免全屏效果
        min_width, min_height = self.get_scaled_size(1200, 800)
        default_width, default_height = self.get_scaled_size(1300, 850)

        self.setMinimumSize(min_width, min_height)
        self.resize(default_width, default_height)

        # 确保窗口不会超过屏幕尺寸的80%
        self.limit_window_size()

        # 居中显示
        self.center_window()

        # 设置窗口状态 - 正常窗口，不最大化
        self.setWindowState(Qt.WindowState.WindowNoState)
        
        self.manager = AppManager()
        self.recorder = None
        
        self.setup_ui()
        self.load_settings()

        # 连接交互录制信号
        self.record_interactions_btn.clicked.connect(self.start_interaction_recording)
        self.load_interactions_btn.clicked.connect(self.load_interaction_file)

        # 设置快捷键
        self.setup_shortcuts()

        # 应用初始主题
        self.apply_initial_theme()

        # 初始化工具管理器
        self.manager.utility_tools = UtilityToolsManager(self)

    def center_window(self):
        """窗口居中显示，支持高DPI"""
        screen = QApplication.primaryScreen()
        screen_geometry = screen.availableGeometry()
        window_geometry = self.frameGeometry()

        # 计算居中位置
        center_point = screen_geometry.center()
        window_geometry.moveCenter(center_point)
        self.move(window_geometry.topLeft())

    def get_scaled_size(self, base_width: int, base_height: int):
        """根据DPI缩放计算合适的窗口尺寸"""
        screen = QApplication.primaryScreen()
        dpi_ratio = screen.devicePixelRatio()
        logical_dpi = screen.logicalDotsPerInch()

        # 基准DPI为96
        scale_factor = logical_dpi / 96.0

        # 计算缩放后的尺寸
        scaled_width = int(base_width / scale_factor)
        scaled_height = int(base_height / scale_factor)

        # 确保最小尺寸
        min_width = int(1200 / scale_factor)
        min_height = int(800 / scale_factor)

        return max(scaled_width, min_width), max(scaled_height, min_height)

    def get_scaled_font_size(self, base_size: int) -> int:
        """根据DPI缩放计算合适的字体大小"""
        screen = QApplication.primaryScreen()
        logical_dpi = screen.logicalDotsPerInch()

        # 基准DPI为96
        scale_factor = logical_dpi / 96.0

        # 对于高DPI，稍微减小字体缩放以避免过大
        if scale_factor > 1.25:
            scale_factor = 1.0 + (scale_factor - 1.0) * 0.8

        scaled_size = int(base_size * scale_factor)
        return max(scaled_size, 8)  # 最小字体大小为8

    def limit_window_size(self):
        """限制窗口大小，确保不会过大"""
        screen = QApplication.primaryScreen()
        screen_geometry = screen.availableGeometry()

        # 获取当前窗口大小
        current_size = self.size()

        # 计算屏幕的80%作为最大尺寸
        max_width = int(screen_geometry.width() * 0.8)
        max_height = int(screen_geometry.height() * 0.8)

        # 如果当前尺寸超过最大尺寸，则调整
        new_width = min(current_size.width(), max_width)
        new_height = min(current_size.height(), max_height)

        if new_width != current_size.width() or new_height != current_size.height():
            self.resize(new_width, new_height)

    def setup_ui(self):
        central = QWidget()
        self.setCentralWidget(central)
        main_layout = QHBoxLayout(central)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(10, 10, 10, 10)

        # 左侧控制面板（添加滚动区域）
        left_scroll = QScrollArea()
        left_scroll.setWidgetResizable(True)
        left_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        left_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        # 根据DPI缩放设置面板宽度 - 使用更小的宽度
        min_panel_width, _ = self.get_scaled_size(380, 100)
        max_panel_width, _ = self.get_scaled_size(420, 100)
        left_scroll.setMinimumWidth(min_panel_width)
        left_scroll.setMaximumWidth(max_panel_width)

        left_panel = self.create_control_panel()
        left_scroll.setWidget(left_panel)
        main_layout.addWidget(left_scroll, 0)

        # 右侧信息面板（标签页）
        right_panel = self.create_info_panel()
        main_layout.addWidget(right_panel, 1)

        # 创建菜单栏
        self.create_menu_bar()

        # 设置状态栏
        self.setup_enhanced_status_bar()
    
    def create_control_panel(self):
        panel = QWidget()  # 改为普通Widget，因为已经在滚动区域中
        layout = QVBoxLayout(panel)
        layout.setSpacing(8)
        layout.setContentsMargins(5, 5, 5, 5)
        
        # Gemini生成器
        gemini_group = QGroupBox("🤖 AI动画生成")
        gemini_layout = QVBoxLayout(gemini_group)
        
        # API Key设置
        api_layout = QHBoxLayout()
        api_layout.addWidget(QLabel("API Key:"))
        self.api_key_input = QLineEdit()
        self.api_key_input.setPlaceholderText("输入Gemini API Key...")
        self.api_key_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.api_key_input.setText(self.manager.config.gemini_api_key)
        # 自动保存API Key
        self.api_key_input.textChanged.connect(self.save_api_key)
        api_layout.addWidget(self.api_key_input)
        gemini_layout.addLayout(api_layout)
        
        # 模型选择和思考模式
        model_layout = QHBoxLayout()
        model_layout.addWidget(QLabel("模型:"))
        self.model_combo = QComboBox()
        self.model_combo.addItems(["gemini-2.5-flash", "gemini-2.5-pro"])
        self.model_combo.setCurrentText(self.manager.config.gemini_model)
        self.model_combo.currentTextChanged.connect(self.save_model_settings)
        model_layout.addWidget(self.model_combo)
        
        self.thinking_checkbox = QCheckBox("深度思考")
        self.thinking_checkbox.setChecked(self.manager.config.enable_thinking)
        self.thinking_checkbox.stateChanged.connect(self.save_model_settings)
        model_layout.addWidget(self.thinking_checkbox)
        gemini_layout.addLayout(model_layout)
        
        # 生成描述
        gemini_layout.addWidget(QLabel("动画描述:"))
        self.prompt_input = QTextEdit()
        self.prompt_input.setPlaceholderText("描述你想要的动画效果，例如：\n- 一个旋转的立方体，颜色从红色渐变到蓝色\n- 粒子从中心向四周扩散的效果\n- 文字逐个弹出的打字机效果")
        self.prompt_input.setMinimumHeight(60)
        self.prompt_input.setMaximumHeight(100)
        gemini_layout.addWidget(self.prompt_input)
        
        # 动画类型选择
        type_layout = QHBoxLayout()
        type_layout.addWidget(QLabel("类型:"))
        self.animation_type_combo = QComboBox()
        self.animation_type_combo.addItems([
            "CSS动画", "GSAP动画", "Three.js动画", 
            "JavaScript动画", "混合动画"
        ])
        type_layout.addWidget(self.animation_type_combo)
        gemini_layout.addLayout(type_layout)
        
        # 生成按钮
        self.generate_btn = QPushButton("🚀 生成HTML动画")
        self.generate_btn.setObjectName("generate_btn")  # 设置objectName
        self.generate_btn.clicked.connect(self.generate_with_gemini)
        gemini_layout.addWidget(self.generate_btn)
        
        # 生成状态
        self.gemini_status = QLabel("等待生成...")
        self.gemini_status.setStyleSheet("color: #666; font-style: italic;")
        gemini_layout.addWidget(self.gemini_status)
        
        layout.addWidget(gemini_group)
        
        # 文件选择
        file_group = QGroupBox("HTML文件")
        file_layout = QVBoxLayout(file_group)
        
        file_row = QHBoxLayout()
        self.file_input = QLineEdit()
        browse_btn = QPushButton("浏览")
        browse_btn.clicked.connect(self.browse_file)
        file_row.addWidget(self.file_input)
        file_row.addWidget(browse_btn)
        file_layout.addLayout(file_row)
        
        # 分析按钮
        analyze_btn = QPushButton("🔍 智能分析")
        analyze_btn.clicked.connect(self.analyze_animation)
        file_layout.addWidget(analyze_btn)
        
        layout.addWidget(file_group)
        
        # 录制配置
        config_group = QGroupBox("📹 录制配置")
        config_layout = QVBoxLayout(config_group)

        # 分辨率行
        res_row = QHBoxLayout()
        res_row.addWidget(QLabel("分辨率:"))
        self.width_input = QSpinBox()
        self.width_input.setRange(320, 4096)
        self.width_input.setValue(self.manager.config.width)
        self.width_input.setMinimumWidth(80)
        self.height_input = QSpinBox()
        self.height_input.setRange(240, 2160)
        self.height_input.setValue(self.manager.config.height)
        self.height_input.setMinimumWidth(80)
        res_row.addWidget(self.width_input)
        res_row.addWidget(QLabel("×"))
        res_row.addWidget(self.height_input)
        res_row.addStretch()
        config_layout.addLayout(res_row)

        # 帧率和总帧数行
        frame_row = QHBoxLayout()
        frame_row.addWidget(QLabel("帧率:"))
        self.fps_input = QSpinBox()
        self.fps_input.setRange(1, 120)
        self.fps_input.setValue(self.manager.config.fps)
        self.fps_input.setMinimumWidth(60)
        self.fps_input.setSuffix(" FPS")
        frame_row.addWidget(self.fps_input)

        frame_row.addWidget(QLabel("总帧数:"))
        self.frames_input = QSpinBox()
        self.frames_input.setRange(1, 18000)
        self.frames_input.setValue(self.manager.config.total_frames)
        self.frames_input.setMinimumWidth(80)
        frame_row.addWidget(self.frames_input)
        frame_row.addStretch()
        config_layout.addLayout(frame_row)

        # 输出路径行
        output_row = QHBoxLayout()
        output_row.addWidget(QLabel("输出:"))
        self.output_input = QLineEdit(self.manager.config.output_path)
        self.output_input.setPlaceholderText("选择输出文件路径...")
        output_btn = QPushButton("📁")
        output_btn.setMaximumWidth(40)
        output_btn.clicked.connect(self.browse_output)
        output_row.addWidget(self.output_input)
        output_row.addWidget(output_btn)
        config_layout.addLayout(output_row)
        
        layout.addWidget(config_group)

        # 交互式录制配置
        interaction_group = QGroupBox("🎮 交互式录制")
        interaction_layout = QVBoxLayout(interaction_group)

        # 动画类型检测
        self.animation_mode_label = QLabel("检测模式: 时间驱动")
        interaction_layout.addWidget(self.animation_mode_label)

        # 交互事件配置
        events_layout = QHBoxLayout()
        self.record_interactions_btn = QPushButton("📝 录制交互序列")
        self.load_interactions_btn = QPushButton("📁 加载交互文件")
        events_layout.addWidget(self.record_interactions_btn)
        events_layout.addWidget(self.load_interactions_btn)
        interaction_layout.addLayout(events_layout)

        # 预览交互序列
        self.interactions_list = QListWidget()
        self.interactions_list.setMaximumHeight(100)
        interaction_layout.addWidget(QLabel("交互序列:"))
        interaction_layout.addWidget(self.interactions_list)

        layout.addWidget(interaction_group)

        # 录制控制
        record_group = QGroupBox("🎬 录制控制")
        record_layout = QVBoxLayout(record_group)

        # 录制按钮行
        record_btn_layout = QHBoxLayout()
        self.record_btn = QPushButton("🚀 开始录制")
        self.record_btn.setObjectName("record_btn")
        self.record_btn.setMinimumHeight(45)
        self.record_btn.clicked.connect(self.start_recording)

        self.stop_btn = QPushButton("⏹️ 停止录制")
        self.stop_btn.setObjectName("stop_btn")
        self.stop_btn.setMinimumHeight(45)
        self.stop_btn.clicked.connect(self.stop_recording)
        self.stop_btn.setEnabled(False)

        record_btn_layout.addWidget(self.record_btn)
        record_btn_layout.addWidget(self.stop_btn)
        record_layout.addLayout(record_btn_layout)

        # 进度显示
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        record_layout.addWidget(self.progress_bar)
        
        # 进度条
        self.progress_bar = QProgressBar()
        record_layout.addWidget(self.progress_bar)
        
        layout.addWidget(record_group)
        layout.addStretch()
        
        return panel
    
    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu("文件")
        file_menu.addAction("新建HTML", self.new_html_file)
        file_menu.addAction("打开HTML", self.browse_file)
        file_menu.addSeparator()
        file_menu.addAction("保存配置", self.save_settings)
        file_menu.addAction("退出", self.close)
        
        # AI菜单
        ai_menu = menubar.addMenu("AI生成")
        ai_menu.addAction("🤖 Gemini生成", self.show_gemini_dialog)
        ai_menu.addAction("⚙️ 设置API Key", self.set_api_key)
        ai_menu.addAction("🎯 快速模板", self.show_quick_templates)
        
        # 预览菜单
        preview_menu = menubar.addMenu("预览")
        preview_menu.addAction("🎬 打开预览窗口", self.open_preview_window)
        preview_menu.addAction("🌐 在浏览器中预览", self.preview_in_browser)

        # 工具菜单 (新增)
        tools_menu = menubar.addMenu("🔧 工具")
        tools_menu.addAction("🔧 HTML优化器", self.open_html_optimizer)
        tools_menu.addAction("📊 性能监控", self.toggle_performance_monitoring)
        tools_menu.addSeparator()
        tools_menu.addAction("📦 库管理器", self.open_library_manager)
        tools_menu.addAction("📁 项目管理器", self.open_project_manager)
        tools_menu.addSeparator()

        # 实用工具子菜单
        utility_menu = tools_menu.addMenu("🛠️ 实用工具")
        utility_menu.addAction("📐 分辨率计算器", self.open_resolution_calculator)
        utility_menu.addAction("🎨 颜色选择器", self.open_color_picker)
        utility_menu.addAction("🔄 格式转换器", self.open_format_converter)
        utility_menu.addAction("🎞️ 帧数计算器", self.open_frame_calculator)
        utility_menu.addAction("📊 文件大小计算器", self.open_file_size_calculator)

        # 主题菜单 (新增)
        theme_menu = menubar.addMenu("🎨 主题")
        theme_menu.addAction("☀️ 浅色主题", lambda: self.change_theme('light'))
        theme_menu.addAction("🌙 深色主题", lambda: self.change_theme('dark'))
        theme_menu.addAction("💙 蓝色主题", lambda: self.change_theme('blue'))

        # 视图菜单 (新增)
        view_menu = menubar.addMenu("👁️ 视图")
        view_menu.addAction("🔍 最大化窗口", self.toggle_maximize)
        view_menu.addAction("📐 重置窗口大小", self.reset_window_size)
        view_menu.addSeparator()
        view_menu.addAction("🔄 刷新界面", self.refresh_ui)

        # 帮助菜单
        help_menu = menubar.addMenu("帮助")
        help_menu.addAction("📖 使用说明", self.show_help)
        help_menu.addAction("🎯 Gemini模板", self.show_gemini_templates)
        help_menu.addAction("ℹ️ 关于", self.show_about)
    
    def create_info_panel(self):
        panel = QTabWidget()
        panel.setTabPosition(QTabWidget.TabPosition.North)

        # 检测结果标签页
        detection_tab = QWidget()
        detection_layout = QVBoxLayout(detection_tab)
        detection_layout.setSpacing(8)
        detection_layout.setContentsMargins(8, 8, 8, 8)

        # 检测结果显示
        detection_group = QGroupBox("🎯 动画检测结果")
        detection_group_layout = QVBoxLayout(detection_group)
        self.detection_text = QTextEdit()
        self.detection_text.setReadOnly(True)
        self.detection_text.setMaximumHeight(200)
        self.detection_text.setPlaceholderText("点击'智能分析'按钮来检测HTML动画类型...")
        detection_group_layout.addWidget(self.detection_text)
        detection_layout.addWidget(detection_group)

        # 控制代码
        code_group = QGroupBox("📝 控制代码")
        code_layout = QVBoxLayout(code_group)

        self.code_text = QTextEdit()
        font_size = self.get_scaled_font_size(9)
        self.code_text.setFont(QFont("Consolas", font_size))
        self.code_text.setPlaceholderText("生成的控制代码将在此显示...")
        code_layout.addWidget(self.code_text)

        # 代码操作按钮
        code_btn_layout = QHBoxLayout()
        apply_btn = QPushButton("📝 应用到HTML")
        apply_btn.clicked.connect(self.apply_code)
        copy_btn = QPushButton("📋 复制代码")
        copy_btn.clicked.connect(self.copy_code)
        code_btn_layout.addWidget(apply_btn)
        code_btn_layout.addWidget(copy_btn)
        code_btn_layout.addStretch()
        code_layout.addLayout(code_btn_layout)

        detection_layout.addWidget(code_group)
        panel.addTab(detection_tab, "🎯 检测结果")
        
        # HTML预览标签页
        html_tab = QWidget()
        html_layout = QVBoxLayout(html_tab)
        html_layout.setSpacing(8)
        html_layout.setContentsMargins(8, 8, 8, 8)

        # HTML代码显示
        html_group = QGroupBox("📄 生成的HTML代码")
        html_group_layout = QVBoxLayout(html_group)

        self.html_preview = QTextEdit()
        font_size = self.get_scaled_font_size(9)
        self.html_preview.setFont(QFont("Consolas", font_size))
        self.html_preview.setPlaceholderText("AI生成的HTML代码将在此显示...")
        html_group_layout.addWidget(self.html_preview)
        html_layout.addWidget(html_group)

        # HTML操作按钮
        html_btn_layout = QHBoxLayout()
        save_html_btn = QPushButton("💾 保存为HTML")
        save_html_btn.clicked.connect(self.save_generated_html)
        load_html_btn = QPushButton("📁 加载到录制")
        load_html_btn.clicked.connect(self.load_generated_html)
        preview_html_btn = QPushButton("👁️ 预览动画")
        preview_html_btn.clicked.connect(self.preview_generated_html)
        copy_html_btn = QPushButton("📋 复制HTML")
        copy_html_btn.clicked.connect(self.copy_html)

        html_btn_layout.addWidget(save_html_btn)
        html_btn_layout.addWidget(load_html_btn)
        html_btn_layout.addWidget(preview_html_btn)
        html_btn_layout.addWidget(copy_html_btn)
        html_btn_layout.addStretch()
        html_layout.addLayout(html_btn_layout)

        panel.addTab(html_tab, "📄 HTML代码")
        
        # 动画预览标签页
        self.preview_controller = AnimationPreviewController()
        panel.addTab(self.preview_controller, "🎬 动画预览")
        
        # 日志标签页
        log_tab = QWidget()
        log_layout = QVBoxLayout(log_tab)
        log_layout.setSpacing(8)
        log_layout.setContentsMargins(8, 8, 8, 8)

        # 日志控制
        log_control_layout = QHBoxLayout()
        clear_log_btn = QPushButton("🗑️ 清空日志")
        clear_log_btn.clicked.connect(self.clear_log)
        save_log_btn = QPushButton("💾 保存日志")
        save_log_btn.clicked.connect(self.save_log)
        log_control_layout.addWidget(clear_log_btn)
        log_control_layout.addWidget(save_log_btn)
        log_control_layout.addStretch()
        log_layout.addLayout(log_control_layout)

        # 日志显示
        log_group = QGroupBox("📋 系统日志")
        log_group_layout = QVBoxLayout(log_group)

        self.log_text = QPlainTextEdit()
        font_size = self.get_scaled_font_size(9)
        self.log_text.setFont(QFont("Consolas", font_size))
        self.log_text.setReadOnly(True)
        self.log_text.setPlaceholderText("系统日志将在此显示...")
        log_group_layout.addWidget(self.log_text)
        log_layout.addWidget(log_group)

        panel.addTab(log_tab, "📋 日志")

        # 性能监控标签页
        performance_tab = QWidget()
        performance_layout = QVBoxLayout(performance_tab)

        # 添加性能监控小部件
        performance_widget = self.create_performance_widget()
        performance_layout.addWidget(performance_widget)

        # 性能历史图表（简化版）
        history_group = QGroupBox("📈 性能历史")
        history_layout = QVBoxLayout(history_group)

        self.performance_history_text = QPlainTextEdit()
        self.performance_history_text.setReadOnly(True)
        self.performance_history_text.setMaximumHeight(200)
        history_layout.addWidget(self.performance_history_text)

        performance_layout.addWidget(history_group)
        performance_layout.addStretch()

        panel.addTab(performance_tab, "📊 性能监控")

        return panel
    
    def save_api_key(self):
        """自动保存API Key"""
        self.manager.config.gemini_api_key = self.api_key_input.text()
        self.save_settings()
    
    def save_model_settings(self):
        """保存模型设置"""
        self.manager.config.gemini_model = self.model_combo.currentText()
        self.manager.config.enable_thinking = self.thinking_checkbox.isChecked()
        self.save_settings()
    
    def new_html_file(self):
        """创建新HTML文件"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "新建HTML文件", "animation.html", "HTML文件 (*.html *.htm)"
        )
        if file_path:
            basic_html = """<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>动画演示</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f0f0f0;
            font-family: Arial, sans-serif;
        }
        .container {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 500px;
        }
        .box {
            width: 100px;
            height: 100px;
            background: #ff6b6b;
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="box" id="animatedBox"></div>
    </div>
    
    <script>
        // 动画控制函数
        function renderAtTime(t) {
            const duration = 10; // 10秒动画
            const progress = Math.min(t / duration, 1);
            
            const box = document.getElementById('animatedBox');
            if (box) {
                // 简单的移动和旋转动画
                const translateX = progress * 300;
                const rotate = progress * 360;
                box.style.transform = `translateX(${translateX}px) rotate(${rotate}deg)`;
            }
        }
        
        // 挂载到window对象
        window.renderAtTime = renderAtTime;
    </script>
</body>
</html>"""
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(basic_html)
                self.file_input.setText(file_path)
                self.manager.config.html_file = file_path
                self.log(f"✅ 新HTML文件已创建: {file_path}")
            except Exception as e:
                self.log(f"❌ 创建文件失败: {e}")
                QMessageBox.critical(self, "错误", f"创建文件失败: {e}")
    
    def show_gemini_dialog(self):
        """显示Gemini生成对话框"""
        if not GEMINI_AVAILABLE:
            QMessageBox.warning(self, "Gemini不可用", "Gemini库未安装，请运行: pip install google-generativeai")
            return
        
        dialog = QDialog(self)
        dialog.setWindowTitle("🤖 Gemini AI生成")
        dialog.setMinimumSize(600, 450)
        layout = QVBoxLayout(dialog)
        
        # 模型和思考设置
        settings_layout = QHBoxLayout()
        settings_layout.addWidget(QLabel("模型:"))
        model_combo = QComboBox()
        model_combo.addItems(["gemini-2.5-flash", "gemini-2.5-pro"])
        model_combo.setCurrentText(self.manager.config.gemini_model)
        settings_layout.addWidget(model_combo)
        
        thinking_check = QCheckBox("启用深度思考")
        thinking_check.setChecked(self.manager.config.enable_thinking)
        settings_layout.addWidget(thinking_check)
        settings_layout.addStretch()
        layout.addLayout(settings_layout)
        
        # 描述输入
        layout.addWidget(QLabel("动画描述:"))
        prompt_edit = QTextEdit()
        prompt_edit.setPlaceholderText("请详细描述你想要的动画效果...")
        layout.addWidget(prompt_edit)
        
        # 类型选择
        type_layout = QHBoxLayout()
        type_layout.addWidget(QLabel("动画类型:"))
        type_combo = QComboBox()
        type_combo.addItems(["CSS动画", "GSAP动画", "Three.js动画", "JavaScript动画", "混合动画"])
        type_layout.addWidget(type_combo)
        layout.addLayout(type_layout)
        
        # 按钮
        btn_layout = QHBoxLayout()
        generate_btn = QPushButton("生成")
        cancel_btn = QPushButton("取消")
        btn_layout.addWidget(generate_btn)
        btn_layout.addWidget(cancel_btn)
        layout.addLayout(btn_layout)
        
        def on_generate():
            # 更新主界面设置
            self.model_combo.setCurrentText(model_combo.currentText())
            self.thinking_checkbox.setChecked(thinking_check.isChecked())
            self.prompt_input.setPlainText(prompt_edit.toPlainText())
            self.animation_type_combo.setCurrentText(type_combo.currentText())
            self.save_model_settings()
            dialog.accept()
            self.generate_with_gemini()
        
        generate_btn.clicked.connect(on_generate)
        cancel_btn.clicked.connect(dialog.reject)
        
        dialog.exec()
    
    def show_quick_templates(self):
        """显示快速模板"""
        dialog = QDialog(self)
        dialog.setWindowTitle("🎯 快速动画模板")
        dialog.setMinimumSize(700, 500)
        layout = QVBoxLayout(dialog)
        
        # 模板列表
        template_list = QListWidget()
        templates = [
            ("旋转方块", "一个彩色方块绕中心旋转360度，同时改变颜色从红色到蓝色", "CSS动画"),
            ("弹跳球", "一个小球在屏幕中弹跳，模拟重力效果", "JavaScript动画"),
            ("粒子爆炸", "从中心点爆发出多个彩色粒子向四周扩散", "JavaScript动画"),
            ("3D立方体", "一个3D立方体在空间中旋转，表面有不同颜色", "Three.js动画"),
            ("打字机效果", "文字逐个字符出现，模拟打字机效果", "CSS动画"),
            ("GSAP时间轴", "多个元素按序列执行不同的动画效果", "GSAP动画"),
            ("波浪动画", "创建水波纹或正弦波动画效果", "JavaScript动画"),
            ("加载动画", "创建旋转的加载指示器", "CSS动画"),
            ("粒子星空", "满屏随机移动的星星粒子效果", "JavaScript动画"),
            ("3D场景漫游", "摄像机在3D场景中移动浏览", "Three.js动画")
        ]
        
        for name, desc, type_name in templates:
            item = QListWidgetItem(f"【{type_name}】{name}")
            item.setData(32, (name, desc, type_name))  # Qt.UserRole
            template_list.addItem(item)
        
        layout.addWidget(template_list)
        
        # 预览区域
        preview_text = QTextEdit()
        preview_text.setReadOnly(True)
        preview_text.setMaximumHeight(100)
        layout.addWidget(QLabel("模板描述:"))
        layout.addWidget(preview_text)
        
        def on_selection_changed():
            current = template_list.currentItem()
            if current:
                name, desc, type_name = current.data(32)
                preview_text.setText(f"动画名称: {name}\n类型: {type_name}\n描述: {desc}")
        
        template_list.itemSelectionChanged.connect(on_selection_changed)
        
        # 按钮
        btn_layout = QHBoxLayout()
        use_btn = QPushButton("使用模板")
        cancel_btn = QPushButton("取消")
        btn_layout.addWidget(use_btn)
        btn_layout.addWidget(cancel_btn)
        layout.addLayout(btn_layout)
        
        def on_use_template():
            current = template_list.currentItem()
            if current:
                name, desc, type_name = current.data(32)
                self.prompt_input.setPlainText(desc)
                self.animation_type_combo.setCurrentText(type_name)
                dialog.accept()
        
        use_btn.clicked.connect(on_use_template)
        cancel_btn.clicked.connect(dialog.reject)
        
        dialog.exec()
    
    def set_api_key(self):
        """设置API Key"""
        api_key, ok = QInputDialog.getText(
            self, "设置Gemini API Key", 
            "请输入你的Gemini API Key:",
            QLineEdit.EchoMode.Password,
            self.manager.config.gemini_api_key
        )
        
        if ok and api_key:
            self.manager.config.gemini_api_key = api_key
            self.api_key_input.setText(api_key)
            self.save_settings()
            self.log("✅ API Key已保存")
    
    def open_preview_window(self):
        """打开独立预览窗口"""
        if not self.manager.config.html_file:
            QMessageBox.warning(self, "无文件", "请先选择HTML文件")
            return
        
        preview_window = QDialog(self)
        preview_window.setWindowTitle("🎬 动画预览")
        preview_window.setMinimumSize(800, 600)
        layout = QVBoxLayout(preview_window)
        
        preview_controller = AnimationPreviewController()
        preview_controller.load_html(self.manager.config.html_file)
        layout.addWidget(preview_controller)
        
        preview_window.exec()
    
    def preview_in_browser(self):
        """在浏览器中预览"""
        if not self.manager.config.html_file:
            QMessageBox.warning(self, "无文件", "请先选择HTML文件")
            return
        
        file_url = f"file:///{os.path.abspath(self.manager.config.html_file)}"
        webbrowser.open(file_url)
        self.log(f"🌐 在浏览器中打开: {file_url}")
    
    def show_gemini_templates(self):
        """显示Gemini生成模板"""
        dialog = QDialog(self)
        dialog.setWindowTitle("🎯 动画规范和提示指南")
        dialog.setMinimumSize(800, 600)
        layout = QVBoxLayout(dialog)
        
        # 创建标签页
        tabs = QTabWidget()
        
        # 规范概览
        overview_tab = QWidget()
        overview_layout = QVBoxLayout(overview_tab)
        overview_text = QTextEdit()
        overview_text.setReadOnly(True)
        overview_text.setPlainText("""动画规范核心要点：

🎯 必须实现的控制函数：
• renderAtTime(t) - 推荐使用
• 其他可选：updateAtTime(t), seekTo(t), goToTime(t)
• 必须挂载到window对象：window.renderAtTime = renderAtTime

🚫 严格禁止的做法：
• 自动播放：setInterval、requestAnimationFrame循环
• 实时时间：Date.now()、performance.now()  
• 异步动画：setTimeout、Promise延迟

⚡ 性能要求：
• 控制函数快速执行
• 缓存DOM元素，避免重复查询
• 包含边界检查：Math.min(Math.max(t/duration, 0), 1)
• 支持无头浏览器运行

📐 技术规范：
• 时间参数t单位为秒，浮点数
• 起始值0.0，范围0到动画总时长
• 函数调用后立即渲染对应状态
• 禁用所有自动播放机制

🎨 动画类型说明：
• CSS动画：只使用CSS样式变换，不能混用其他库
• GSAP动画：只使用GSAP库，不能混用CSS动画
• Three.js动画：只使用Three.js，不能混用2D动画
• JavaScript动画：只使用原生JS，不能使用任何库
• 混合动画：可以组合多种技术，统一在renderAtTime中控制
""")
        overview_layout.addWidget(overview_text)
        tabs.addTab(overview_tab, "📋 规范概览")
        
        # CSS动画指南
        css_tab = QWidget()
        css_layout = QVBoxLayout(css_tab)
        css_text = QTextEdit()
        css_text.setReadOnly(True)
        css_text.setPlainText("""CSS动画提示词指南：

✅ 正确的描述方式：
• "创建一个方块从左到右移动的动画，通过renderAtTime控制"
• "制作颜色渐变效果，禁用CSS animation属性"
• "实现旋转和缩放组合动画，手动计算每帧状态"

🔧 技术要求：
• 移除或注释所有animation属性
• 在renderAtTime中使用element.style直接设置样式
• 手动计算变换值：translateX、rotate、scale等
• 使用progress = Math.min(t / duration, 1)计算进度

💡 描述模板：
• "一个{颜色}{形状}，{运动方式}，时长{秒数}秒，禁用CSS自动动画"
• "实现{元素}的{属性变化}，通过JavaScript手动控制进度"

🚫 注意：选择CSS动画类型时，不要在描述中提及GSAP、Three.js等其他技术
""")
        css_layout.addWidget(css_text)
        tabs.addTab(css_tab, "🎨 CSS动画")
        
        # GSAP指南
        gsap_tab = QWidget()
        gsap_layout = QVBoxLayout(gsap_tab)
        gsap_text = QTextEdit()
        gsap_text.setReadOnly(True)
        gsap_text.setPlainText("""GSAP动画提示词指南：

✅ 正确的描述方式：
• "使用GSAP创建时间轴动画，设置paused模式，通过seek控制"
• "制作GSAP缓动动画，禁用autoplay，使用timeline.seek(t)"
• "实现复杂动画序列，创建主时间轴统一控制"

🔧 技术要求：
• 引入GSAP库：cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js
• 创建暂停的时间轴：gsap.timeline({paused: true})
• 使用timeline.seek(t)方法控制进度
• 禁用循环和自动播放

💡 描述模板：
• "GSAP时间轴动画，{元素}执行{动作}，使用{缓动函数}，通过seek控制"
• "创建GSAP动画序列，{第一个动作}然后{第二个动作}，paused模式"

🚫 注意：选择GSAP动画类型时，不要在描述中提及CSS动画、Three.js等其他技术
""")
        gsap_layout.addWidget(gsap_text)
        tabs.addTab(gsap_tab, "⚡ GSAP动画")
        
        # Three.js指南
        threejs_tab = QWidget()
        threejs_layout = QVBoxLayout(threejs_tab)
        threejs_text = QTextEdit()
        threejs_text.setReadOnly(True)
        threejs_text.setPlainText("""Three.js动画提示词指南：

✅ 正确的描述方式：
• "Three.js 3D场景，{对象}在空间中{运动}，移除动画循环"
• "创建3D{形状}动画，手动控制渲染，根据时间参数更新状态"
• "3D粒子系统，禁用requestAnimationFrame，通过renderAtTime控制"

🔧 技术要求：
• 引入Three.js库：cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js
• 移除requestAnimationFrame动画循环
• 在renderAtTime中更新对象属性并调用renderer.render()
• 根据时间参数控制相机、光照、材质等

💡 描述模板：
• "Three.js {3D对象}，{旋转/移动/变形}动画，手动渲染控制"
• "3D场景包含{元素}，{光照效果}，通过时间参数驱动动画"

🚫 注意：选择Three.js动画类型时，不要在描述中提及CSS、GSAP等2D动画技术
""")
        threejs_layout.addWidget(threejs_text)
        tabs.addTab(threejs_tab, "🌐 Three.js")
        
        # JavaScript指南
        js_tab = QWidget()
        js_layout = QVBoxLayout(js_tab)
        js_text = QTextEdit()
        js_text.setReadOnly(True)
        js_text.setPlainText("""JavaScript动画提示词指南：

✅ 正确的描述方式：
• "纯JavaScript动画，{效果描述}，禁用定时器，手动控制进度"
• "DOM操作动画，{元素变化}，通过renderAtTime函数驱动"
• "Canvas绘图动画，{绘制内容}，根据时间参数重绘"

🔧 技术要求：
• 禁用setInterval、setTimeout、requestAnimationFrame
• 直接操作DOM元素的style属性
• 缓存DOM查询结果避免重复查找
• 使用数学函数计算动画状态

💡 描述模板：
• "JavaScript {动画类型}，{具体效果}，手动计算状态，禁用定时器"
• "Canvas动画，绘制{图形/效果}，根据时间参数清除重绘"

🚫 注意：选择JavaScript动画类型时，不要在描述中提及任何外部库
""")
        js_layout.addWidget(js_text)
        tabs.addTab(js_tab, "⚙️ JavaScript")
        
        # 混合动画指南
        mixed_tab = QWidget()
        mixed_layout = QVBoxLayout(mixed_tab)
        mixed_text = QTextEdit()
        mixed_text.setReadOnly(True)
        mixed_text.setPlainText("""混合动画提示词指南：

✅ 正确的描述方式：
• "结合CSS和GSAP创建复杂动画，CSS控制背景，GSAP控制元素运动"
• "Three.js 3D场景配合CSS界面动画，统一时间控制"
• "JavaScript粒子系统加上CSS文字动画，同步播放"

🔧 技术要求：
• 可以同时使用多种动画技术
• 在单一renderAtTime(t)函数中统一控制所有组件
• 确保不同技术间的时间同步
• 合理分配时间段给不同的动画效果

💡 描述模板：
• "混合动画：{技术1}负责{效果1}，{技术2}负责{效果2}，统一时间控制"
• "组合{技术列表}实现{复合效果}，通过renderAtTime同步所有动画"

✅ 注意：只有选择混合动画类型时，才能在描述中同时提及多种技术
""")
        mixed_layout.addWidget(mixed_text)
        tabs.addTab(mixed_tab, "🎭 混合动画")
        
        layout.addWidget(tabs)
        
        # 关闭按钮
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(dialog.close)
        layout.addWidget(close_btn)
        
        dialog.exec()
    
    def show_help(self):
        """显示帮助信息"""
        help_text = """
🎬 网页动画录制工具 v6.0 - 使用指南

🤖 AI生成功能：
1. 设置Gemini API Key（会自动保存）
2. 选择模型：Flash（快速）或Pro（高质量）
3. 可选择是否启用深度思考模式
4. 严格选择动画类型：
   • CSS动画：只生成CSS样式动画
   • GSAP动画：只使用GSAP库
   • Three.js动画：只生成3D动画
   • JavaScript动画：只使用原生JS
   • 混合动画：可组合多种技术
5. 输入详细的动画描述
6. 点击"生成HTML动画"

🎬 预览功能：
• 内置预览：在"动画预览"标签页中控制播放
• 独立窗口：菜单->预览->打开预览窗口
• 浏览器预览：菜单->预览->在浏览器中预览
• 主动控制：通过时间滑块精确控制动画进度

📋 录制流程：
1. 有HTML文件后，点击"智能分析"
2. 查看检测结果和控制代码
3. 应用控制代码到HTML（如需要）
4. 在"动画预览"中测试效果
5. 设置录制参数（分辨率、帧率等）
6. 点击"开始录制"

💡 使用技巧：
• 所有设置（API Key、模型选择等）会自动保存
• 描述动画时要具体，包含颜色、形状、运动方式
• 严格按动画类型选择，避免技术混用
• 可以使用快速模板开始
• 预览确认效果后再录制

🔧 技术要求：
• 需要安装FFmpeg
• 需要有效的Gemini API Key
• 生成的HTML会自动包含renderAtTime函数
        """
        
        QMessageBox.information(self, "使用帮助", help_text)
    
    def show_about(self):
        """显示关于信息"""
        about_text = """
🎬 网页动画录制工具 v6.0
集成AI生成和预览功能

✨ 主要特性：
• 🤖 智能AI生成HTML动画
• 🎯 严格的动画类型控制
• 🎬 实时动画预览功能
• ⚡ 主动控制录制技术
• 💾 自动保存所有设置
• 📊 实时进度监控

🔧 技术栈：
• Python + PyQt6
• Playwright自动化
• FFmpeg视频编码
• Google Gemini AI
• WebEngine预览

💡 v6.0更新内容：
• 新增模型选择（Flash/Pro）
• 新增深度思考开关
• 新增动画预览控制器
• 新增严格类型约束
• 新增设置自动保存
• 新增快速模板库
• 优化用户体验

感谢使用！
        """
        
        QMessageBox.information(self, "关于", about_text)
    
    def generate_with_gemini(self):
        """使用Gemini生成HTML动画"""
        if not GEMINI_AVAILABLE:
            QMessageBox.warning(self, "Gemini不可用", "Gemini库未安装，请运行: pip install google-generativeai")
            return
        
        # 更新API Key
        self.manager.config.gemini_api_key = self.api_key_input.text()
        
        if not self.manager.config.gemini_api_key:
            QMessageBox.warning(self, "API Key缺失", "请先设置Gemini API Key")
            return
        
        prompt = self.prompt_input.toPlainText().strip()
        if not prompt:
            QMessageBox.warning(self, "描述为空", "请输入动画描述")
            return
        
        animation_type = self.animation_type_combo.currentText()
        
        # 保存当前设置
        self.save_model_settings()
        
        # 创建并启动生成器
        generator = self.manager.generate_html_with_gemini(prompt, animation_type)
        generator.result_ready.connect(self.on_gemini_result)
        generator.error_occurred.connect(self.on_gemini_error)
        generator.progress_update.connect(self.on_gemini_progress)
        
        generator.start()
        model_info = f"{self.manager.config.gemini_model}"
        thinking_info = "启用思考" if self.manager.config.enable_thinking else "禁用思考"
        self.log(f"🤖 开始生成 {animation_type} 动画... (模型: {model_info}, {thinking_info})")
    
    def on_gemini_result(self, html_content):
        """Gemini生成完成"""
        self.html_preview.setPlainText(html_content)
        self.gemini_status.setText("✅ 生成完成")
        self.log("✅ Gemini生成完成")
        
        # 自动切换到HTML预览标签页
        info_panel = self.findChild(QTabWidget)
        if info_panel:
            info_panel.setCurrentIndex(1)  # HTML预览是第二个标签页
    
    def on_gemini_error(self, error_msg):
        """Gemini生成错误"""
        self.gemini_status.setText(f"❌ 生成失败")
        self.log(f"❌ Gemini生成失败: {error_msg}")
        QMessageBox.critical(self, "生成失败", error_msg)
    
    def on_gemini_progress(self, status):
        """Gemini生成进度更新"""
        self.gemini_status.setText(status)
        self.log(status)
    
    def save_generated_html(self):
        """保存生成的HTML"""
        html_content = self.html_preview.toPlainText()
        if not html_content:
            QMessageBox.warning(self, "无内容", "没有HTML内容可保存")
            return
        
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存HTML文件", "generated_animation.html", "HTML文件 (*.html *.htm)"
        )
        
        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(html_content)
                self.log(f"✅ HTML已保存: {file_path}")
                QMessageBox.information(self, "保存成功", f"HTML已保存到: {file_path}")
            except Exception as e:
                self.log(f"❌ 保存失败: {e}")
                QMessageBox.critical(self, "保存失败", f"保存失败: {e}")
    
    def load_generated_html(self):
        """加载生成的HTML到录制"""
        html_content = self.html_preview.toPlainText()
        if not html_content:
            QMessageBox.warning(self, "无内容", "没有HTML内容可加载")
            return
        
        # 创建临时文件
        temp_file = "temp_generated_animation.html"
        try:
            with open(temp_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            self.file_input.setText(temp_file)
            self.manager.config.html_file = temp_file
            self.log(f"✅ HTML已加载到录制: {temp_file}")
            
            # 自动分析动画
            self.analyze_animation()
            
            # 自动加载到预览
            self.preview_controller.load_html(temp_file)
            
        except Exception as e:
            self.log(f"❌ 加载失败: {e}")
            QMessageBox.critical(self, "加载失败", f"加载失败: {e}")
    
    def preview_generated_html(self):
        """预览生成的HTML"""
        html_content = self.html_preview.toPlainText()
        if not html_content:
            QMessageBox.warning(self, "无内容", "没有HTML内容可预览")
            return
        
        # 创建临时文件用于预览
        with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as f:
            f.write(html_content)
            temp_file = f.name
        
        try:
            # 加载到预览控制器
            self.preview_controller.load_html(temp_file)
            
            # 切换到预览标签页
            info_panel = self.findChild(QTabWidget)
            if info_panel:
                info_panel.setCurrentIndex(2)  # 预览是第三个标签页
            
            self.log(f"✅ 已加载到预览: {temp_file}")
            
        except Exception as e:
            self.log(f"❌ 预览失败: {e}")
            QMessageBox.critical(self, "预览失败", f"预览失败: {e}")
    
    def browse_file(self):
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择HTML文件", "", "HTML文件 (*.html *.htm)"
        )
        if file_path:
            self.file_input.setText(file_path)
            self.manager.config.html_file = file_path
            # 自动加载到预览
            self.preview_controller.load_html(file_path)
            self.save_settings()
    
    def browse_output(self):
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存视频", "", "MP4文件 (*.mp4)"
        )
        if file_path:
            self.output_input.setText(file_path)
            self.manager.config.output_path = file_path
            self.save_settings()
    
    def analyze_animation(self):
        if not self.manager.config.html_file:
            QMessageBox.warning(self, "错误", "请先选择HTML文件")
            return
        
        try:
            with open(self.manager.config.html_file, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            self.manager.detection = self.manager.detect_animation(html_content)
            
            result_text = f"""
🎯 检测结果:
类型: {self.manager.detection.type.value}
置信度: {self.manager.detection.confidence:.2f}

💡 建议:
{chr(10).join(f"• {s}" for s in self.manager.detection.suggestions)}
            """
            
            self.detection_text.setPlainText(result_text.strip())
            self.code_text.setPlainText(self.manager.detection.control_code)
            
            self.log(f"✅ 分析完成: {self.manager.detection.type.value}")
            
        except Exception as e:
            self.log(f"❌ 分析失败: {e}")
            QMessageBox.critical(self, "错误", f"分析失败: {e}")
    
    def apply_code(self):
        if not self.code_text.toPlainText():
            QMessageBox.warning(self, "错误", "没有控制代码可应用")
            return
        
        try:
            # 读取HTML文件
            with open(self.manager.config.html_file, 'r', encoding='utf-8') as f:
                html = f.read()
            
            # 插入控制代码
            code = self.code_text.toPlainText()
            if '</body>' in html:
                html = html.replace('</body>', f'<script>\n{code}\n</script>\n</body>')
            else:
                html += f'\n<script>\n{code}\n</script>'
            
            # 保存备份
            backup_path = self.manager.config.html_file + '.backup'
            with open(backup_path, 'w', encoding='utf-8') as f:
                f.write(html)
            
            # 保存修改后的文件
            with open(self.manager.config.html_file, 'w', encoding='utf-8') as f:
                f.write(html)
            
            # 重新加载到预览
            self.preview_controller.load_html(self.manager.config.html_file)
            
            self.log(f"✅ 控制代码已应用，备份: {backup_path}")
            QMessageBox.information(self, "成功", "控制代码已应用到HTML文件")
            
        except Exception as e:
            self.log(f"❌ 应用失败: {e}")
            QMessageBox.critical(self, "错误", f"应用失败: {e}")
    
    def start_recording(self):
        # 更新配置
        self.update_config()
        
        if not self.manager.config.html_file:
            QMessageBox.warning(self, "错误", "请选择HTML文件")
            return
        
        # 创建录制器
        self.recorder = Recorder(self.manager.config)
        self.recorder.progress.connect(self.update_progress)
        self.recorder.log.connect(self.log)
        self.recorder.finished.connect(self.on_finished)
        
        # 更新UI
        self.record_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.progress_bar.setMaximum(self.manager.config.total_frames)
        
        # 开始录制
        self.recorder.start()
        self.log("🚀 开始录制...")
    
    def stop_recording(self):
        if self.recorder:
            self.recorder.stop()
            self.log("⏹️ 停止录制...")
    
    def update_progress(self, current, total):
        self.progress_bar.setValue(current)
        self.progress_bar.setFormat(f"{current}/{total} ({current/total*100:.1f}%)")
    
    def on_finished(self, result):
        self.record_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.progress_bar.setValue(0)
        
        if result.startswith("失败"):
            QMessageBox.critical(self, "录制失败", result)
        else:
            QMessageBox.information(self, "录制完成", f"视频已保存: {result}")
        
        self.recorder = None
    
    def update_config(self):
        self.manager.config.width = self.width_input.value()
        self.manager.config.height = self.height_input.value()
        self.manager.config.fps = self.fps_input.value()
        self.manager.config.total_frames = self.frames_input.value()
        self.manager.config.output_path = self.output_input.text()
        self.manager.config.html_file = self.file_input.text()
    
    def load_settings(self):
        self.file_input.setText(self.manager.config.html_file)
        self.output_input.setText(self.manager.config.output_path)
        self.api_key_input.setText(self.manager.config.gemini_api_key)
        self.model_combo.setCurrentText(self.manager.config.gemini_model)
        self.thinking_checkbox.setChecked(self.manager.config.enable_thinking)
        
        # 如果有HTML文件，自动加载到预览
        if self.manager.config.html_file and os.path.exists(self.manager.config.html_file):
            self.preview_controller.load_html(self.manager.config.html_file)
    
    def save_settings(self):
        self.update_config()
        self.manager.config.save()
    
    def log(self, message):
        timestamp = datetime.now().strftime('%H:%M:%S')
        self.log_text.appendPlainText(f"[{timestamp}] {message}")
    
    def closeEvent(self, event):
        if self.recorder:
            self.recorder.stop()
            self.recorder.wait(3000)
        self.save_settings()
        event.accept()

    def start_interaction_recording(self):
        """开始录制用户交互"""
        if not self.manager.config.html_file:
            QMessageBox.warning(self, "错误", "请先选择HTML文件")
            return

        # 创建交互录制窗口
        self.interaction_window = InteractionRecordingWindow(
            self.manager.config.html_file, self
        )
        self.interaction_window.interactions_recorded.connect(self.on_interactions_recorded)
        self.interaction_window.show()

    def load_interaction_file(self):
        """加载交互文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择交互序列文件", "", "JSON文件 (*.json)"
        )
        if file_path:
            self.manager.config.interaction_file = file_path
            self.load_interactions_preview(file_path)

    def load_interactions_preview(self, file_path):
        """预览交互序列"""
        try:
            with open(file_path, 'r') as f:
                events = json.load(f)

            self.interactions_list.clear()
            for event in events:
                item_text = f"{event['timestamp']:.1f}s - {event['event_type']} - {event.get('target_element', 'N/A')}"
                self.interactions_list.addItem(item_text)

            self.log(f"✅ 已加载 {len(events)} 个交互事件")
        except Exception as e:
            self.log(f"❌ 加载交互文件失败: {e}")

    def on_interactions_recorded(self, events_file):
        """交互录制完成"""
        self.manager.config.interaction_file = events_file
        self.load_interactions_preview(events_file)
        self.log(f"✅ 交互序列已录制: {events_file}")

    # === 性能监控方法 ===
    def create_performance_widget(self) -> QWidget:
        """创建性能监控小部件"""
        widget = QGroupBox("📊 性能监控")
        layout = QVBoxLayout(widget)

        # 性能指标显示
        metrics_layout = QHBoxLayout()

        self.cpu_label = QLabel("CPU: 0%")
        self.cpu_progress = QProgressBar()
        self.cpu_progress.setMaximum(100)
        self.cpu_progress.setTextVisible(False)
        self.cpu_progress.setMaximumHeight(15)

        self.memory_label = QLabel("内存: 0%")
        self.memory_progress = QProgressBar()
        self.memory_progress.setMaximum(100)
        self.memory_progress.setTextVisible(False)
        self.memory_progress.setMaximumHeight(15)

        cpu_layout = QVBoxLayout()
        cpu_layout.addWidget(self.cpu_label)
        cpu_layout.addWidget(self.cpu_progress)

        memory_layout = QVBoxLayout()
        memory_layout.addWidget(self.memory_label)
        memory_layout.addWidget(self.memory_progress)

        metrics_layout.addLayout(cpu_layout)
        metrics_layout.addLayout(memory_layout)
        layout.addLayout(metrics_layout)

        # 控制按钮
        controls_layout = QHBoxLayout()
        self.monitor_btn = QPushButton("▶ 开始监控")
        self.monitor_btn.clicked.connect(self.toggle_performance_monitoring)
        self.optimize_btn = QPushButton("🚀 优化系统")
        self.optimize_btn.clicked.connect(self.optimize_system_performance)

        controls_layout.addWidget(self.monitor_btn)
        controls_layout.addWidget(self.optimize_btn)
        layout.addLayout(controls_layout)

        widget.setMaximumHeight(120)
        return widget

    def setup_performance_monitoring(self):
        """设置性能监控"""
        self.performance_monitor = PerformanceMonitor()
        self.performance_monitor.metrics_updated.connect(self.update_performance_display)
        self.performance_monitor.warning_issued.connect(self.show_performance_warning)

        self.recording_performance_monitor = RecordingPerformanceMonitor()

    def toggle_performance_monitoring(self):
        """切换性能监控"""
        if hasattr(self, 'performance_monitor') and self.performance_monitor.monitoring:
            self.performance_monitor.stop_monitoring()
            self.monitor_btn.setText("▶ 开始监控")
            self.log("⏹ 性能监控已停止")
        else:
            if not hasattr(self, 'performance_monitor'):
                self.setup_performance_monitoring()
            self.performance_monitor.start_monitoring()
            self.monitor_btn.setText("⏸ 停止监控")
            self.log("▶ 性能监控已启动")

    def update_performance_display(self, metrics: PerformanceMetrics):
        """更新性能显示"""
        # 更新CPU
        self.cpu_label.setText(f"CPU: {metrics.cpu_percent:.1f}%")
        self.cpu_progress.setValue(int(metrics.cpu_percent))

        # 根据CPU使用率设置颜色
        if metrics.cpu_percent > 80:
            self.cpu_progress.setStyleSheet("QProgressBar::chunk { background-color: #ff4444; }")
        elif metrics.cpu_percent > 60:
            self.cpu_progress.setStyleSheet("QProgressBar::chunk { background-color: #ffaa00; }")
        else:
            self.cpu_progress.setStyleSheet("QProgressBar::chunk { background-color: #44aa44; }")

        # 更新内存
        self.memory_label.setText(f"内存: {metrics.memory_percent:.1f}%")
        self.memory_progress.setValue(int(metrics.memory_percent))

        # 根据内存使用率设置颜色
        if metrics.memory_percent > 85:
            self.memory_progress.setStyleSheet("QProgressBar::chunk { background-color: #ff4444; }")
        elif metrics.memory_percent > 70:
            self.memory_progress.setStyleSheet("QProgressBar::chunk { background-color: #ffaa00; }")
        else:
            self.memory_progress.setStyleSheet("QProgressBar::chunk { background-color: #44aa44; }")

        # 更新历史记录显示
        if hasattr(self, 'performance_history_text'):
            timestamp = datetime.now().strftime('%H:%M:%S')
            history_line = f"[{timestamp}] CPU: {metrics.cpu_percent:.1f}% | 内存: {metrics.memory_percent:.1f}% | {metrics.memory_mb:.0f}MB"
            self.performance_history_text.appendPlainText(history_line)

            # 限制历史记录行数
            if self.performance_history_text.document().blockCount() > 50:
                cursor = self.performance_history_text.textCursor()
                cursor.movePosition(cursor.MoveOperation.Start)
                cursor.select(cursor.SelectionType.BlockUnderCursor)
                cursor.removeSelectedText()

    def show_performance_warning(self, warning_type: str, message: str):
        """显示性能警告"""
        self.log(f"⚠️ {warning_type}警告: {message}")
        # 可以选择是否弹出警告对话框
        if hasattr(self, 'show_performance_popups') and self.show_performance_popups:
            QMessageBox.warning(self, f"{warning_type}警告", message)

    def optimize_system_performance(self):
        """优化系统性能"""
        optimizations = []

        # 垃圾回收
        import gc
        gc.collect()
        optimizations.append("✅ 执行垃圾回收")

        # 清理临时文件
        temp_files = list(Path(".").glob("temp_*.html"))
        for temp_file in temp_files:
            try:
                temp_file.unlink()
                optimizations.append(f"✅ 删除临时文件: {temp_file.name}")
            except:
                pass

        # 内存优化建议
        if hasattr(self, 'performance_monitor'):
            recent_metrics = self.performance_monitor.get_average_metrics(30)
            if recent_metrics.memory_percent > 70:
                optimizations.append("💡 建议: 关闭不必要的应用程序")
            if recent_metrics.cpu_percent > 70:
                optimizations.append("💡 建议: 降低录制质量或帧率")

        result = "🚀 系统优化完成:\n\n" + "\n".join(optimizations)
        QMessageBox.information(self, "系统优化", result)
        self.log("🚀 执行系统性能优化")

    # === 项目管理方法 ===
    def new_project(self):
        """新建项目"""
        name, ok = QInputDialog.getText(self, "新建项目", "项目名称:")
        if ok and name:
            description, _ = QInputDialog.getText(self, "项目描述", "描述 (可选):")
            self.log(f"✅ 项目 '{name}' 已创建")
            QMessageBox.information(self, "成功", f"项目 '{name}' 已创建")

    def open_project_manager(self):
        """打开项目管理器"""
        QMessageBox.information(self, "项目管理", "项目管理功能开发中...")

    def save_current_project(self):
        """保存当前项目"""
        QMessageBox.information(self, "保存项目", "项目保存功能开发中...")

    def export_current_project(self):
        """导出当前项目"""
        QMessageBox.information(self, "导出项目", "项目导出功能开发中...")

    def import_project(self):
        """导入项目"""
        QMessageBox.information(self, "导入项目", "项目导入功能开发中...")

    # === 库管理方法 ===
    def open_library_manager(self):
        """打开库管理器"""
        QMessageBox.information(self, "库管理", "库管理功能开发中...")

    def check_library_status(self):
        """检查当前HTML的库状态"""
        QMessageBox.information(self, "库状态", "库状态检查功能开发中...")

    def auto_fix_libraries(self):
        """自动修复缺失的库"""
        QMessageBox.information(self, "自动修复", "自动修复功能开发中...")

    def download_all_libraries(self):
        """下载所有库"""
        QMessageBox.information(self, "下载库", "批量下载功能开发中...")

    # === HTML优化器方法 ===
    def open_html_optimizer(self):
        """打开HTML优化器"""
        if not self.manager.config.html_file:
            QMessageBox.warning(self, "错误", "请先选择HTML文件")
            return

        dialog = HTMLOptimizerDialog(self.manager.config.html_file, self)
        dialog.exec()

    # === 快捷键系统 ===
    def setup_shortcuts(self):
        """设置快捷键"""
        shortcuts = {
            'Ctrl+N': self.new_html_file,
            'Ctrl+O': self.browse_file,
            'Ctrl+S': self.save_settings,
            'Ctrl+G': self.show_gemini_dialog,
            'Ctrl+R': self.start_recording,
            'F5': self.analyze_animation,
            'Ctrl+Shift+P': self.open_project_manager,
            'Ctrl+Shift+L': self.open_library_manager,
            'Ctrl+Shift+S': self.save_current_project,
            'Ctrl+Q': self.close,
            'F1': self.show_help,
            'Ctrl+,': self.open_preferences,
            'Esc': self.cancel_current_operation,
            'Space': self.toggle_preview_play,
            'Ctrl+1': lambda: self.switch_tab(0),  # 检测结果
            'Ctrl+2': lambda: self.switch_tab(1),  # HTML预览
            'Ctrl+3': lambda: self.switch_tab(2),  # 动画预览
            'Ctrl+4': lambda: self.switch_tab(3),  # 日志
            'Ctrl+5': lambda: self.switch_tab(4),  # 性能监控
        }

        for key_sequence, callback in shortcuts.items():
            shortcut = QShortcut(QKeySequence(key_sequence), self)
            shortcut.activated.connect(callback)

        # 在状态栏显示快捷键提示
        self.update_status_bar_shortcuts()

    def update_status_bar_shortcuts(self):
        """更新状态栏快捷键提示"""
        shortcuts_text = "Ctrl+G:生成 | Ctrl+R:录制 | F5:分析 | F1:帮助 | Space:播放/暂停"
        if hasattr(self, 'shortcuts_label'):
            self.shortcuts_label.setText(shortcuts_text)
        else:
            # 创建状态栏快捷键标签
            self.shortcuts_label = QLabel(shortcuts_text)
            self.statusBar().addWidget(self.shortcuts_label)

    def cancel_current_operation(self):
        """取消当前操作"""
        if self.recorder and self.recorder.isRunning():
            self.stop_recording()
            self.log("⏹ 录制已取消")
        elif hasattr(self, 'gemini_generator') and self.gemini_generator and self.gemini_generator.isRunning():
            self.gemini_generator.terminate()
            self.log("⏹ AI生成已取消")
        else:
            self.log("⚠️ 没有正在进行的操作")

    def toggle_preview_play(self):
        """切换预览播放（空格键）"""
        if hasattr(self, 'preview_controller'):
            self.preview_controller.toggle_play()

    def switch_tab(self, index: int):
        """切换标签页"""
        info_panel = self.findChild(QTabWidget)
        if info_panel and index < info_panel.count():
            info_panel.setCurrentIndex(index)

    def open_preferences(self):
        """打开偏好设置"""
        dialog = PreferencesDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            self.log("⚙️ 偏好设置已更新")

    def show_help(self):
        """显示帮助"""
        help_text = """
🎬 网页动画录制工具 - 使用帮助

📋 快捷键列表:
• Ctrl+N          新建HTML文件
• Ctrl+O          打开HTML文件
• Ctrl+S          保存配置
• Ctrl+G          AI生成动画
• Ctrl+R          开始录制
• F5              智能分析动画
• Ctrl+Shift+P    项目管理器
• Ctrl+Shift+L    库管理器
• Ctrl+Q          退出程序
• F1              显示帮助
• Space           播放/暂停预览
• Ctrl+1/2/3/4/5  切换标签页

🚀 使用流程:
1. 选择或生成HTML动画文件
2. 智能分析动画类型 (F5)
3. 配置录制参数
4. 开始录制 (Ctrl+R)
5. 查看输出视频

💡 提示:
• 使用AI生成功能快速创建动画
• 项目管理功能便于组织工作
• 性能监控帮助优化录制质量
        """
        QMessageBox.information(self, "使用帮助", help_text.strip())

    # === 主题管理方法 ===
    def change_theme(self, theme_name: str):
        """切换主题"""
        try:
            app = QApplication.instance()
            self.manager.theme_manager.apply_theme(app, theme_name)
            self.log(f"🎨 已切换到{self.manager.theme_manager.themes[theme_name]['name']}")

            # 更新按钮的objectName以应用特殊样式
            self.update_button_styles()

        except Exception as e:
            self.log(f"❌ 主题切换失败: {e}")

    def update_button_styles(self):
        """更新按钮样式"""
        # 为特殊按钮设置objectName
        if hasattr(self, 'record_btn'):
            self.record_btn.setObjectName("record_btn")
        if hasattr(self, 'stop_btn'):
            self.stop_btn.setObjectName("stop_btn")
        if hasattr(self, 'generate_btn'):
            self.generate_btn.setObjectName("generate_btn")

        # 刷新样式
        self.style().unpolish(self)
        self.style().polish(self)
        self.update()

    def apply_initial_theme(self):
        """应用初始主题"""
        app = QApplication.instance()
        self.manager.theme_manager.apply_theme(app, 'light')
        self.update_button_styles()

    # === 视图控制方法 ===
    def toggle_maximize(self):
        """切换最大化状态"""
        if self.isMaximized():
            self.showNormal()
            self.log("📐 窗口已恢复正常大小")
        else:
            self.showMaximized()
            self.log("🔍 窗口已最大化")

    def reset_window_size(self):
        """重置窗口大小"""
        # 恢复到默认大小
        default_width, default_height = self.get_scaled_size(1300, 850)
        self.resize(default_width, default_height)
        self.limit_window_size()
        self.center_window()
        self.log("📐 窗口大小已重置")

    def refresh_ui(self):
        """刷新界面"""
        self.update()
        self.repaint()
        self.log("🔄 界面已刷新")

    # === 实用工具方法 ===
    def open_resolution_calculator(self):
        """打开分辨率计算器"""
        dialog = self.manager.utility_tools.resolution_calculator()
        dialog.exec()

    def open_color_picker(self):
        """打开颜色选择器"""
        dialog = self.manager.utility_tools.color_picker()
        dialog.exec()

    def open_format_converter(self):
        """打开格式转换器"""
        dialog = self.manager.utility_tools.format_converter()
        dialog.exec()

    def open_frame_calculator(self):
        """打开帧数计算器"""
        dialog = self.manager.utility_tools.frame_calculator()
        dialog.exec()

    def open_file_size_calculator(self):
        """打开文件大小计算器"""
        dialog = self.manager.utility_tools.file_size_calculator()
        dialog.exec()

    # 实现工具管理器需要的方法
    def set_resolution_from_tool(self, width: int, height: int):
        """从工具设置分辨率"""
        self.width_input.setValue(width)
        self.height_input.setValue(height)
        self.log(f"📐 分辨率已设置为 {width}x{height}")

    def apply_resolution_from_tool(self, width: int, height: int):
        """应用工具设置的分辨率"""
        self.manager.config.width = width
        self.manager.config.height = height
        self.set_resolution_from_tool(width, height)
        self.log(f"✅ 分辨率配置已应用: {width}x{height}")

    def setup_enhanced_status_bar(self):
        """设置增强状态栏"""
        status_bar = self.statusBar()
        status_bar.setStyleSheet("QStatusBar { border-top: 1px solid #ccc; }")

        # 版本信息
        version_label = QLabel("v7.0")
        version_label.setStyleSheet("color: #666; font-weight: bold; margin: 0 10px;")
        status_bar.addPermanentWidget(version_label)

        # 性能信息区域
        self.status_cpu_label = QLabel("CPU: 0%")
        self.status_memory_label = QLabel("内存: 0%")
        self.status_cpu_label.setStyleSheet("color: #44aa44; font-weight: bold; margin: 0 5px;")
        self.status_memory_label.setStyleSheet("color: #44aa44; font-weight: bold; margin: 0 5px;")
        status_bar.addPermanentWidget(self.status_cpu_label)
        status_bar.addPermanentWidget(self.status_memory_label)

        # 快捷键提示
        self.shortcuts_label = QLabel()
        status_bar.addWidget(self.shortcuts_label)
        self.update_status_bar_shortcuts()

    def update_status_bar_performance(self, metrics):
        """更新状态栏性能信息"""
        if hasattr(self, 'status_cpu_label') and hasattr(self, 'status_memory_label'):
            self.status_cpu_label.setText(f"CPU: {metrics.cpu_percent:.0f}%")
            self.status_memory_label.setText(f"内存: {metrics.memory_percent:.0f}%")

            # 根据性能设置颜色
            cpu_color = "#ff4444" if metrics.cpu_percent > 80 else "#44aa44"
            memory_color = "#ff4444" if metrics.memory_percent > 85 else "#44aa44"

            self.status_cpu_label.setStyleSheet(f"color: {cpu_color}; font-weight: bold; margin: 0 5px;")
            self.status_memory_label.setStyleSheet(f"color: {memory_color}; font-weight: bold; margin: 0 5px;")

    def copy_code(self):
        """复制代码到剪贴板"""
        code = self.code_text.toPlainText()
        if code.strip():
            QApplication.clipboard().setText(code)
            self.log("📋 代码已复制到剪贴板")
        else:
            self.log("⚠️ 没有代码可复制")

    def copy_html(self):
        """复制HTML到剪贴板"""
        html = self.html_preview.toPlainText()
        if html.strip():
            QApplication.clipboard().setText(html)
            self.log("📋 HTML代码已复制到剪贴板")
        else:
            self.log("⚠️ 没有HTML代码可复制")

    def clear_log(self):
        """清空日志"""
        self.log_text.clear()
        self.log("🗑️ 日志已清空")

    def save_log(self):
        """保存日志"""
        log_content = self.log_text.toPlainText()
        if log_content.strip():
            file_path, _ = QFileDialog.getSaveFileName(
                self, "保存日志", "log.txt", "文本文件 (*.txt);;所有文件 (*)"
            )
            if file_path:
                try:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(log_content)
                    self.log(f"💾 日志已保存到: {file_path}")
                except Exception as e:
                    self.log(f"❌ 保存日志失败: {e}")
        else:
            self.log("⚠️ 没有日志内容可保存")

# === 偏好设置对话框 ===
class PreferencesDialog(QDialog):
    """偏好设置对话框"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setup_ui()
        self.load_preferences()

    def setup_ui(self):
        self.setWindowTitle("⚙️ 偏好设置")
        self.setMinimumSize(500, 400)
        layout = QVBoxLayout(self)

        # 标签页
        tabs = QTabWidget()

        # 常规设置
        general_tab = QWidget()
        general_layout = QVBoxLayout(general_tab)

        # 性能警告开关
        self.performance_warnings_check = QCheckBox("显示性能警告弹窗")
        general_layout.addWidget(self.performance_warnings_check)

        # 自动保存间隔
        auto_save_layout = QHBoxLayout()
        auto_save_layout.addWidget(QLabel("自动保存间隔:"))
        self.auto_save_spinbox = QSpinBox()
        self.auto_save_spinbox.setRange(0, 600)
        self.auto_save_spinbox.setValue(30)
        self.auto_save_spinbox.setSuffix(" 秒")
        auto_save_layout.addWidget(self.auto_save_spinbox)
        auto_save_layout.addStretch()
        general_layout.addLayout(auto_save_layout)

        # 主题选择
        theme_layout = QHBoxLayout()
        theme_layout.addWidget(QLabel("主题:"))
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["系统默认", "浅色主题", "深色主题"])
        theme_layout.addWidget(self.theme_combo)
        theme_layout.addStretch()
        general_layout.addLayout(theme_layout)

        general_layout.addStretch()
        tabs.addTab(general_tab, "常规")

        # 快捷键设置
        shortcuts_tab = QWidget()
        shortcuts_layout = QVBoxLayout(shortcuts_tab)

        shortcuts_layout.addWidget(QLabel("快捷键列表 (只读):"))
        shortcuts_text = QTextEdit()
        shortcuts_text.setReadOnly(True)
        shortcuts_text.setPlainText("""
Ctrl+N          新建HTML文件
Ctrl+O          打开HTML文件
Ctrl+S          保存配置
Ctrl+G          AI生成动画
Ctrl+R          开始录制
F5              智能分析动画
Ctrl+Shift+P    项目管理器
Ctrl+Shift+L    库管理器
Ctrl+Q          退出程序
F1              显示帮助
Space           播放/暂停预览
Ctrl+1/2/3/4/5  切换标签页
        """.strip())
        shortcuts_layout.addWidget(shortcuts_text)

        tabs.addTab(shortcuts_tab, "快捷键")

        # 高级设置
        advanced_tab = QWidget()
        advanced_layout = QVBoxLayout(advanced_tab)

        # 日志级别
        log_level_layout = QHBoxLayout()
        log_level_layout.addWidget(QLabel("日志级别:"))
        self.log_level_combo = QComboBox()
        self.log_level_combo.addItems(["详细", "正常", "简洁"])
        log_level_layout.addWidget(self.log_level_combo)
        log_level_layout.addStretch()
        advanced_layout.addLayout(log_level_layout)

        # 实验性功能
        self.experimental_features_check = QCheckBox("启用实验性功能")
        advanced_layout.addWidget(self.experimental_features_check)

        advanced_layout.addStretch()
        tabs.addTab(advanced_tab, "高级")

        layout.addWidget(tabs)

        # 按钮
        buttons_layout = QHBoxLayout()
        self.ok_btn = QPushButton("确定")
        self.cancel_btn = QPushButton("取消")
        self.reset_btn = QPushButton("重置")

        buttons_layout.addStretch()
        buttons_layout.addWidget(self.reset_btn)
        buttons_layout.addWidget(self.cancel_btn)
        buttons_layout.addWidget(self.ok_btn)

        layout.addLayout(buttons_layout)

        # 连接信号
        self.ok_btn.clicked.connect(self.accept)
        self.cancel_btn.clicked.connect(self.reject)
        self.reset_btn.clicked.connect(self.reset_preferences)

    def load_preferences(self):
        """加载偏好设置"""
        # 从配置文件加载设置
        pass

    def reset_preferences(self):
        """重置偏好设置"""
        reply = QMessageBox.question(
            self, "确认重置",
            "确定要重置所有设置为默认值吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        if reply == QMessageBox.StandardButton.Yes:
            self.performance_warnings_check.setChecked(True)
            self.auto_save_spinbox.setValue(30)
            self.theme_combo.setCurrentText("系统默认")
            self.log_level_combo.setCurrentText("正常")
            self.experimental_features_check.setChecked(False)

# === HTML优化器对话框 ===
class HTMLOptimizerDialog(QDialog):
    """HTML优化器对话框"""

    def __init__(self, html_file: str, parent=None):
        super().__init__(parent)
        self.html_file = html_file
        self.optimizer = HTMLOptimizer()
        self.setup_ui()
        self.load_html_content()

    def setup_ui(self):
        self.setWindowTitle("🔧 HTML优化器")
        self.setMinimumSize(800, 600)
        layout = QVBoxLayout(self)

        # 优化选项
        options_group = QGroupBox("优化选项")
        options_layout = QVBoxLayout(options_group)

        self.remove_comments_check = QCheckBox("移除注释")
        self.remove_comments_check.setChecked(True)
        self.minify_css_check = QCheckBox("压缩CSS")
        self.minify_css_check.setChecked(True)
        self.minify_js_check = QCheckBox("压缩JavaScript")
        self.minify_js_check.setChecked(True)
        self.remove_empty_lines_check = QCheckBox("移除空行")
        self.remove_empty_lines_check.setChecked(True)
        self.validate_render_check = QCheckBox("验证/添加renderAtTime函数")
        self.validate_render_check.setChecked(True)
        self.add_meta_check = QCheckBox("添加meta标签")
        self.add_meta_check.setChecked(True)

        options_layout.addWidget(self.remove_comments_check)
        options_layout.addWidget(self.minify_css_check)
        options_layout.addWidget(self.minify_js_check)
        options_layout.addWidget(self.remove_empty_lines_check)
        options_layout.addWidget(self.validate_render_check)
        options_layout.addWidget(self.add_meta_check)

        layout.addWidget(options_group)

        # 预览区域
        preview_group = QGroupBox("优化预览")
        preview_layout = QVBoxLayout(preview_group)

        self.preview_text = QTextEdit()
        # 获取主窗口实例来使用缩放字体
        main_window = None
        for widget in QApplication.topLevelWidgets():
            if isinstance(widget, MainWindow):
                main_window = widget
                break

        if main_window:
            font_size = main_window.get_scaled_font_size(9)
        else:
            font_size = 9

        self.preview_text.setFont(QFont("Consolas", font_size))
        self.preview_text.setReadOnly(True)
        preview_layout.addWidget(self.preview_text)

        layout.addWidget(preview_group)

        # 统计信息
        self.stats_label = QLabel("统计信息将在此显示")
        layout.addWidget(self.stats_label)

        # 按钮
        buttons_layout = QHBoxLayout()
        self.preview_btn = QPushButton("🔍 预览优化")
        self.apply_btn = QPushButton("✅ 应用优化")
        self.cancel_btn = QPushButton("❌ 取消")

        buttons_layout.addWidget(self.preview_btn)
        buttons_layout.addWidget(self.apply_btn)
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.cancel_btn)

        layout.addLayout(buttons_layout)

        # 连接信号
        self.preview_btn.clicked.connect(self.preview_optimization)
        self.apply_btn.clicked.connect(self.apply_optimization)
        self.cancel_btn.clicked.connect(self.reject)

        # 自动连接复选框变化
        for checkbox in [self.remove_comments_check, self.minify_css_check,
                        self.minify_js_check, self.remove_empty_lines_check,
                        self.validate_render_check, self.add_meta_check]:
            checkbox.stateChanged.connect(self.preview_optimization)

    def load_html_content(self):
        """加载HTML内容"""
        try:
            with open(self.html_file, 'r', encoding='utf-8') as f:
                self.original_html = f.read()
            self.preview_optimization()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载HTML文件失败: {e}")

    def preview_optimization(self):
        """预览优化结果"""
        if not hasattr(self, 'original_html'):
            return

        options = {
            'remove_comments': self.remove_comments_check.isChecked(),
            'minify_css': self.minify_css_check.isChecked(),
            'minify_js': self.minify_js_check.isChecked(),
            'remove_empty_lines': self.remove_empty_lines_check.isChecked(),
            'validate_render_function': self.validate_render_check.isChecked(),
            'add_meta_tags': self.add_meta_check.isChecked()
        }

        self.optimized_html = self.optimizer.optimize_html(self.original_html, options)
        self.preview_text.setPlainText(self.optimized_html)

        # 更新统计信息
        report = self.optimizer.get_optimization_report(self.original_html, self.optimized_html)
        stats_text = f"""
原始大小: {report['original_size']} 字符
优化后大小: {report['optimized_size']} 字符
压缩率: {report['compression_ratio']:.1f}%
减少行数: {report['lines_reduced']}
包含renderAtTime函数: {'是' if report['has_render_function'] else '否'}
验证问题: {len(report['validation_issues'])} 个
        """.strip()

        if report['validation_issues']:
            stats_text += f"\n问题列表: {', '.join(report['validation_issues'])}"

        self.stats_label.setText(stats_text)

    def apply_optimization(self):
        """应用优化"""
        if not hasattr(self, 'optimized_html'):
            self.preview_optimization()

        reply = QMessageBox.question(
            self, "确认优化",
            "确定要应用优化吗？原文件将被备份。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            try:
                # 备份原文件
                backup_path = self.html_file + '.backup'
                with open(backup_path, 'w', encoding='utf-8') as f:
                    f.write(self.original_html)

                # 保存优化后的文件
                with open(self.html_file, 'w', encoding='utf-8') as f:
                    f.write(self.optimized_html)

                QMessageBox.information(self, "成功", f"HTML已优化，备份文件: {backup_path}")
                self.accept()

            except Exception as e:
                QMessageBox.critical(self, "错误", f"应用优化失败: {e}")

def main():
    # 高DPI支持配置 - 必须在QApplication创建之前设置
    import os

    # 设置Qt高DPI属性 (PyQt6版本)
    try:
        # PyQt6中的高DPI设置
        QApplication.setHighDpiScaleFactorRoundingPolicy(Qt.HighDpiScaleFactorRoundingPolicy.PassThrough)
    except AttributeError:
        # 如果属性不存在，跳过
        pass

    app = QApplication(sys.argv)

    # 设置应用程序属性以更好地处理DPI缩放
    try:
        # 尝试设置一些有用的属性
        app.setAttribute(Qt.ApplicationAttribute.AA_DontCreateNativeWidgetSiblings, True)
    except AttributeError:
        # 如果属性不存在，跳过
        pass

    # 检查依赖
    missing_deps = []
    
    # 检查FFmpeg
    try:
        subprocess.run(['ffmpeg', '-version'], capture_output=True, check=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        missing_deps.append("FFmpeg")
    
    # 检查Playwright
    if not PLAYWRIGHT_AVAILABLE:
        missing_deps.append("Playwright")
    
    # 检查Gemini (可选)
    if not GEMINI_AVAILABLE:
        print("⚠️ Gemini库未安装，AI生成功能将不可用")
        print("   可运行: pip install google-generativeai")
    
    if missing_deps:
        error_msg = f"缺少依赖项: {', '.join(missing_deps)}\n\n"
        if "FFmpeg" in missing_deps:
            error_msg += "FFmpeg安装方法:\n"
            error_msg += "• Windows: 下载解压并设置PATH\n"
            error_msg += "• macOS: brew install ffmpeg\n"
            error_msg += "• Linux: apt install ffmpeg\n\n"
        if "Playwright" in missing_deps:
            error_msg += "Playwright安装方法:\n"
            error_msg += "pip install playwright\n"
            error_msg += "playwright install\n"
        
        QMessageBox.critical(None, "依赖缺失", error_msg)
        sys.exit(1)
    
    window = MainWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()