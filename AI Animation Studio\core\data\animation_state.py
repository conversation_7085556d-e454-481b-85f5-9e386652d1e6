# AI Animation Studio - 动画状态管理
# 版权所有 (c) 2024 AI Animation Studio

import json
import time
from dataclasses import dataclass, field, asdict
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime

from .enums import AnimationType, ElementType, PathType, StateType

@dataclass
class TransformState:
    """变换状态"""
    translateX: float = 0.0
    translateY: float = 0.0
    translateZ: float = 0.0
    rotateX: float = 0.0
    rotateY: float = 0.0
    rotateZ: float = 0.0
    scaleX: float = 1.0
    scaleY: float = 1.0
    scaleZ: float = 1.0
    skewX: float = 0.0
    skewY: float = 0.0

@dataclass
class VisualState:
    """视觉状态"""
    opacity: float = 1.0
    color: str = "#000000"
    background_color: str = "transparent"
    border_color: str = "transparent"
    border_width: str = "0px"
    border_radius: str = "0px"
    box_shadow: str = "none"
    text_shadow: str = "none"

@dataclass
class LayoutState:
    """布局状态"""
    width: str = "auto"
    height: str = "auto"
    position: str = "absolute"
    top: str = "auto"
    left: str = "auto"
    right: str = "auto"
    bottom: str = "auto"
    z_index: int = 1
    display: str = "block"
    visibility: str = "visible"

@dataclass
class EffectState:
    """效果状态"""
    filter: str = "none"
    backdrop_filter: str = "none"
    clip_path: str = "none"
    mask: str = "none"
    mix_blend_mode: str = "normal"

@dataclass
class AnimationProperties:
    """动画属性"""
    duration: float = 1.0
    delay: float = 0.0
    timing_function: str = "ease"
    iteration_count: str = "1"
    direction: str = "normal"
    fill_mode: str = "forwards"
    play_state: str = "running"

@dataclass
class ElementState:
    """元素状态"""
    element_id: str
    element_type: ElementType
    timestamp: float = field(default_factory=time.time)
    state_type: StateType = StateType.INITIAL
    
    # 状态数据
    transform: TransformState = field(default_factory=TransformState)
    visual: VisualState = field(default_factory=VisualState)
    layout: LayoutState = field(default_factory=LayoutState)
    effects: EffectState = field(default_factory=EffectState)
    animation: AnimationProperties = field(default_factory=AnimationProperties)
    
    # 自定义属性
    custom_properties: Dict[str, Any] = field(default_factory=dict)
    
    # 元数据
    confidence: float = 1.0
    source: str = "user"  # user, ai, computed
    notes: str = ""
    
    def to_css_dict(self) -> Dict[str, str]:
        """转换为CSS属性字典"""
        css_dict = {}
        
        # 变换属性
        transform_parts = []
        if self.transform.translateX != 0 or self.transform.translateY != 0:
            transform_parts.append(f"translate({self.transform.translateX}px, {self.transform.translateY}px)")
        if self.transform.translateZ != 0:
            transform_parts.append(f"translateZ({self.transform.translateZ}px)")
        if self.transform.rotateX != 0:
            transform_parts.append(f"rotateX({self.transform.rotateX}deg)")
        if self.transform.rotateY != 0:
            transform_parts.append(f"rotateY({self.transform.rotateY}deg)")
        if self.transform.rotateZ != 0:
            transform_parts.append(f"rotateZ({self.transform.rotateZ}deg)")
        if self.transform.scaleX != 1 or self.transform.scaleY != 1:
            transform_parts.append(f"scale({self.transform.scaleX}, {self.transform.scaleY})")
        if self.transform.skewX != 0:
            transform_parts.append(f"skewX({self.transform.skewX}deg)")
        if self.transform.skewY != 0:
            transform_parts.append(f"skewY({self.transform.skewY}deg)")
        
        if transform_parts:
            css_dict['transform'] = ' '.join(transform_parts)
        
        # 视觉属性
        css_dict.update({
            'opacity': str(self.visual.opacity),
            'color': self.visual.color,
            'background-color': self.visual.background_color,
            'border-color': self.visual.border_color,
            'border-width': self.visual.border_width,
            'border-radius': self.visual.border_radius,
            'box-shadow': self.visual.box_shadow,
            'text-shadow': self.visual.text_shadow,
        })
        
        # 布局属性
        css_dict.update({
            'width': self.layout.width,
            'height': self.layout.height,
            'position': self.layout.position,
            'top': self.layout.top,
            'left': self.layout.left,
            'right': self.layout.right,
            'bottom': self.layout.bottom,
            'z-index': str(self.layout.z_index),
            'display': self.layout.display,
            'visibility': self.layout.visibility,
        })
        
        # 效果属性
        css_dict.update({
            'filter': self.effects.filter,
            'backdrop-filter': self.effects.backdrop_filter,
            'clip-path': self.effects.clip_path,
            'mask': self.effects.mask,
            'mix-blend-mode': self.effects.mix_blend_mode,
        })
        
        # 移除默认值
        return {k: v for k, v in css_dict.items() if v not in ['auto', 'none', 'transparent', '0px', '1', 'normal']}
    
    def copy(self) -> 'ElementState':
        """创建状态副本"""
        return ElementState(
            element_id=self.element_id,
            element_type=self.element_type,
            timestamp=time.time(),
            state_type=self.state_type,
            transform=TransformState(**asdict(self.transform)),
            visual=VisualState(**asdict(self.visual)),
            layout=LayoutState(**asdict(self.layout)),
            effects=EffectState(**asdict(self.effects)),
            animation=AnimationProperties(**asdict(self.animation)),
            custom_properties=self.custom_properties.copy(),
            confidence=self.confidence,
            source=self.source,
            notes=self.notes
        )
    
    def interpolate(self, other: 'ElementState', progress: float) -> 'ElementState':
        """在两个状态之间插值"""
        if progress <= 0:
            return self.copy()
        if progress >= 1:
            return other.copy()
        
        # 创建插值状态
        interpolated = self.copy()
        interpolated.timestamp = time.time()
        interpolated.state_type = StateType.INTERMEDIATE
        
        # 插值变换属性
        t = interpolated.transform
        ot = other.transform
        t.translateX = self._lerp(t.translateX, ot.translateX, progress)
        t.translateY = self._lerp(t.translateY, ot.translateY, progress)
        t.translateZ = self._lerp(t.translateZ, ot.translateZ, progress)
        t.rotateX = self._lerp(t.rotateX, ot.rotateX, progress)
        t.rotateY = self._lerp(t.rotateY, ot.rotateY, progress)
        t.rotateZ = self._lerp(t.rotateZ, ot.rotateZ, progress)
        t.scaleX = self._lerp(t.scaleX, ot.scaleX, progress)
        t.scaleY = self._lerp(t.scaleY, ot.scaleY, progress)
        t.scaleZ = self._lerp(t.scaleZ, ot.scaleZ, progress)
        
        # 插值视觉属性
        interpolated.visual.opacity = self._lerp(self.visual.opacity, other.visual.opacity, progress)
        
        return interpolated
    
    def _lerp(self, a: float, b: float, t: float) -> float:
        """线性插值"""
        return a + (b - a) * t

@dataclass
class PathPoint:
    """路径点"""
    x: float
    y: float
    timestamp: float = 0.0
    velocity: float = 0.0
    control_point_1: Optional[Tuple[float, float]] = None
    control_point_2: Optional[Tuple[float, float]] = None

@dataclass
class AnimationPath:
    """动画路径"""
    path_id: str
    path_type: PathType
    points: List[PathPoint] = field(default_factory=list)
    total_length: float = 0.0
    duration: float = 1.0
    easing_function: str = "ease"
    natural_description: str = ""
    
    def add_point(self, x: float, y: float, timestamp: float = None):
        """添加路径点"""
        if timestamp is None:
            timestamp = len(self.points) * 0.1
        
        point = PathPoint(x=x, y=y, timestamp=timestamp)
        self.points.append(point)
        self._update_length()
    
    def _update_length(self):
        """更新路径长度"""
        if len(self.points) < 2:
            self.total_length = 0.0
            return
        
        length = 0.0
        for i in range(1, len(self.points)):
            p1, p2 = self.points[i-1], self.points[i]
            dx = p2.x - p1.x
            dy = p2.y - p1.y
            length += (dx*dx + dy*dy) ** 0.5
        
        self.total_length = length
    
    def get_point_at_progress(self, progress: float) -> Tuple[float, float]:
        """获取指定进度的点坐标"""
        if not self.points:
            return (0.0, 0.0)
        
        if progress <= 0:
            return (self.points[0].x, self.points[0].y)
        if progress >= 1:
            return (self.points[-1].x, self.points[-1].y)
        
        # 简单线性插值
        target_length = progress * self.total_length
        current_length = 0.0
        
        for i in range(1, len(self.points)):
            p1, p2 = self.points[i-1], self.points[i]
            dx = p2.x - p1.x
            dy = p2.y - p1.y
            segment_length = (dx*dx + dy*dy) ** 0.5
            
            if current_length + segment_length >= target_length:
                # 在这个线段内
                segment_progress = (target_length - current_length) / segment_length
                x = p1.x + dx * segment_progress
                y = p1.y + dy * segment_progress
                return (x, y)
            
            current_length += segment_length
        
        return (self.points[-1].x, self.points[-1].y)

@dataclass
class TimeSegment:
    """时间段"""
    segment_id: str
    start_time: float
    end_time: float
    description: str = ""
    narration_text: str = ""
    
    # 动画数据
    animation_type: AnimationType = AnimationType.MOVE
    elements: List[str] = field(default_factory=list)  # 元素ID列表
    animation_path: Optional[AnimationPath] = None
    
    # 状态数据
    initial_states: Dict[str, ElementState] = field(default_factory=dict)
    final_states: Dict[str, ElementState] = field(default_factory=dict)
    
    # 生成的代码
    html_code: str = ""
    css_code: str = ""
    js_code: str = ""
    
    # 元数据
    created_at: str = field(default_factory=lambda: datetime.now().isoformat())
    modified_at: str = field(default_factory=lambda: datetime.now().isoformat())
    ai_generated: bool = False
    ai_model: str = ""
    generation_prompt: str = ""
    
    @property
    def duration(self) -> float:
        """获取时间段长度"""
        return self.end_time - self.start_time
    
    def add_element(self, element_id: str, initial_state: ElementState, final_state: ElementState):
        """添加元素到时间段"""
        if element_id not in self.elements:
            self.elements.append(element_id)
        
        self.initial_states[element_id] = initial_state
        self.final_states[element_id] = final_state
        self.modified_at = datetime.now().isoformat()
    
    def remove_element(self, element_id: str):
        """从时间段移除元素"""
        if element_id in self.elements:
            self.elements.remove(element_id)
        
        self.initial_states.pop(element_id, None)
        self.final_states.pop(element_id, None)
        self.modified_at = datetime.now().isoformat()
    
    def get_element_state_at_time(self, element_id: str, time: float) -> Optional[ElementState]:
        """获取元素在指定时间的状态"""
        if element_id not in self.initial_states or element_id not in self.final_states:
            return None
        
        if time <= self.start_time:
            return self.initial_states[element_id]
        if time >= self.end_time:
            return self.final_states[element_id]
        
        # 计算进度并插值
        progress = (time - self.start_time) / self.duration
        return self.initial_states[element_id].interpolate(self.final_states[element_id], progress)
    
    def validate(self) -> List[str]:
        """验证时间段数据"""
        issues = []
        
        if self.start_time >= self.end_time:
            issues.append("开始时间必须小于结束时间")
        
        if self.duration < 0.1:
            issues.append("时间段长度过短")
        
        if not self.elements:
            issues.append("时间段没有包含任何元素")
        
        for element_id in self.elements:
            if element_id not in self.initial_states:
                issues.append(f"元素 {element_id} 缺少初始状态")
            if element_id not in self.final_states:
                issues.append(f"元素 {element_id} 缺少最终状态")
        
        return issues

@dataclass
class AnimationState:
    """动画状态管理器"""
    project_id: str
    current_time: float = 0.0
    total_duration: float = 30.0
    
    # 时间段管理
    segments: Dict[str, TimeSegment] = field(default_factory=dict)
    segment_order: List[str] = field(default_factory=list)
    
    # 元素状态缓存
    element_states_cache: Dict[str, Dict[float, ElementState]] = field(default_factory=dict)
    
    # 状态历史
    state_history: List[Dict[str, ElementState]] = field(default_factory=list)
    max_history_size: int = 100
    
    def add_segment(self, segment: TimeSegment) -> bool:
        """添加时间段"""
        # 检查时间冲突
        for existing_id in self.segment_order:
            existing = self.segments[existing_id]
            if self._segments_overlap(segment, existing):
                return False
        
        self.segments[segment.segment_id] = segment
        
        # 按时间顺序插入
        inserted = False
        for i, existing_id in enumerate(self.segment_order):
            if segment.start_time < self.segments[existing_id].start_time:
                self.segment_order.insert(i, segment.segment_id)
                inserted = True
                break
        
        if not inserted:
            self.segment_order.append(segment.segment_id)
        
        self._clear_cache()
        return True
    
    def remove_segment(self, segment_id: str) -> bool:
        """移除时间段"""
        if segment_id not in self.segments:
            return False
        
        del self.segments[segment_id]
        self.segment_order.remove(segment_id)
        self._clear_cache()
        return True
    
    def get_segment_at_time(self, time: float) -> Optional[TimeSegment]:
        """获取指定时间的时间段"""
        for segment_id in self.segment_order:
            segment = self.segments[segment_id]
            if segment.start_time <= time <= segment.end_time:
                return segment
        return None
    
    def get_element_state_at_time(self, element_id: str, time: float) -> Optional[ElementState]:
        """获取元素在指定时间的状态"""
        # 检查缓存
        if element_id in self.element_states_cache:
            cache = self.element_states_cache[element_id]
            if time in cache:
                return cache[time]
        
        # 查找包含该时间的时间段
        segment = self.get_segment_at_time(time)
        if segment and element_id in segment.elements:
            state = segment.get_element_state_at_time(element_id, time)
            
            # 缓存结果
            if element_id not in self.element_states_cache:
                self.element_states_cache[element_id] = {}
            self.element_states_cache[element_id][time] = state
            
            return state
        
        return None
    
    def _segments_overlap(self, seg1: TimeSegment, seg2: TimeSegment) -> bool:
        """检查两个时间段是否重叠"""
        return not (seg1.end_time <= seg2.start_time or seg2.end_time <= seg1.start_time)
    
    def _clear_cache(self):
        """清空状态缓存"""
        self.element_states_cache.clear()
    
    def save_state_snapshot(self, states: Dict[str, ElementState]):
        """保存状态快照"""
        self.state_history.append(states.copy())
        
        # 限制历史大小
        if len(self.state_history) > self.max_history_size:
            self.state_history.pop(0)
    
    def get_all_elements(self) -> List[str]:
        """获取所有元素ID"""
        elements = set()
        for segment in self.segments.values():
            elements.update(segment.elements)
        return list(elements)

    def validate_continuity(self) -> List[str]:
        """验证状态连续性"""
        issues = []

        # 按时间顺序检查相邻时间段
        for i in range(len(self.segment_order) - 1):
            current_id = self.segment_order[i]
            next_id = self.segment_order[i + 1]

            current_segment = self.segments[current_id]
            next_segment = self.segments[next_id]

            # 检查时间间隙
            if current_segment.end_time < next_segment.start_time:
                gap = next_segment.start_time - current_segment.end_time
                if gap > 0.1:  # 超过0.1秒的间隙
                    issues.append(f"时间段 {current_id} 和 {next_id} 之间有 {gap:.1f}s 的间隙")

            # 检查共同元素的状态连续性
            common_elements = set(current_segment.elements) & set(next_segment.elements)
            for element_id in common_elements:
                current_final = current_segment.final_states.get(element_id)
                next_initial = next_segment.initial_states.get(element_id)

                if current_final and next_initial:
                    # 检查状态差异
                    diff = self._calculate_state_difference(current_final, next_initial)
                    if diff > 0.1:  # 状态差异阈值
                        issues.append(f"元素 {element_id} 在时间段 {current_id}->{next_id} 状态不连续")

        return issues

    def _calculate_state_difference(self, state1: ElementState, state2: ElementState) -> float:
        """计算两个状态的差异程度"""
        diff = 0.0

        # 位置差异
        dx = state1.transform.translateX - state2.transform.translateX
        dy = state1.transform.translateY - state2.transform.translateY
        position_diff = (dx*dx + dy*dy) ** 0.5
        diff += position_diff / 100.0  # 归一化

        # 旋转差异
        rotation_diff = abs(state1.transform.rotateZ - state2.transform.rotateZ)
        diff += rotation_diff / 360.0  # 归一化

        # 缩放差异
        scale_diff = abs(state1.transform.scaleX - state2.transform.scaleX)
        diff += scale_diff

        # 透明度差异
        opacity_diff = abs(state1.visual.opacity - state2.visual.opacity)
        diff += opacity_diff

        return diff
